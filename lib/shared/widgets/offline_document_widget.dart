import 'dart:io';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:open_file/open_file.dart';
import 'package:storetrack_app/config/routes/app_router.gr.dart';
import 'package:storetrack_app/core/database/realm_database.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/shared/models/downloaded_file_model.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/core/utils/snackbar_service.dart';

/// A reusable widget for handling document opening with offline support
/// Tries to open cached files with default system apps, falls back to WebBrowserRoute
class OfflineDocumentWidget extends StatefulWidget {
  /// The document URL to open
  final String url;

  /// The title to display in browser fallback
  final String title;

  /// The child widget to display (typically a button or list item)
  final Widget child;

  /// Callback fired when the document opening starts
  final VoidCallback? onTap;

  const OfflineDocumentWidget({
    super.key,
    required this.url,
    required this.title,
    required this.child,
    this.onTap,
  });

  @override
  State<OfflineDocumentWidget> createState() => _OfflineDocumentWidgetState();
}

class _OfflineDocumentWidgetState extends State<OfflineDocumentWidget> {
  /// Gets the local file path if available, otherwise returns null
  Future<String?> _getLocalPath(String url) async {
    try {
      final realm = sl<RealmDatabase>().realm;
      final cached = realm.find<DownloadedFileModel>(url);

      if (cached?.isDownloaded == true) {
        final file = File(cached!.localPath);
        if (await file.exists()) {
          // Update last access date for cache management
          realm.write(() => cached.lastAccessDate = DateTime.now());
          return cached.localPath;
        }
      }

      return null; // No local file available
    } catch (e) {
      return null; // Error occurred, use fallback
    }
  }

  /// Opens the document using the appropriate method
  Future<void> _openDocument(BuildContext context) async {
    // Call the onTap callback if provided
    widget.onTap?.call();

    // Check if URL is valid
    if (widget.url.isEmpty) {
      if (mounted) {
        SnackBarService.error(
            context: context, message: 'Document link is not available');
      }
      return;
    }

    // Try to get local file path first
    final localPath = await _getLocalPath(widget.url);

    if (localPath != null) {
      logger('📸 Attempting to open local file: $localPath');
      // Try to open local file with default app
      final result = await _openLocalFile(localPath);
      if (result) {
        return; // Successfully opened locally
      }
    }

    // Fallback to WebBrowserRoute for network URL
    if (mounted) {
      _openInWebBrowser(context);
    }
  }

  /// Attempts to open a local file with the default system app
  Future<bool> _openLocalFile(String localPath) async {
    try {
      final result = await OpenFile.open(localPath);

      // Check if the file was opened successfully
      return result.type == ResultType.done;
    } catch (e) {
      // Failed to open with default app
      return false;
    }
  }

  /// Opens the document in WebBrowserRoute as fallback
  void _openInWebBrowser(BuildContext context) {
    try {
      context.router.push(WebBrowserRoute(
        url: widget.url,
        title: widget.title,
      ));
    } catch (e) {
      SnackBarService.error(
          context: context, message: 'Unable to open document');
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => _openDocument(context),
      child: widget.child,
    );
  }
}
