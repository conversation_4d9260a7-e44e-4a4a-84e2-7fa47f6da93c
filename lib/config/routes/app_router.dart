import 'package:auto_route/auto_route.dart';

// Force regeneration of app_router.gr.dart
import 'app_router.gr.dart';

@AutoRouterConfig(replaceInRouteName: 'Screen|Page,Route')
class AppRouter extends RootStackRouter {
  @override
  RouteType get defaultRouteType => const RouteType.cupertino();

  @override
  List<AutoRoute> get routes => [
        AutoRoute(
          page: ResetPasswordRoute.page,
        ),
        AutoRoute(
          page: SplashRoute.page,
          initial: true,
        ),
        AutoRoute(
          page: LoginRoute.page,
        ),
        AutoRoute(
          page: HomeRoute.page,
          children: [
            AutoRoute(
              page: DashboardHolderRoute.page,
              initial: true,
              children: [
                AutoRoute(
                  page: DashboardRoute.page,
                  initial: true,
                ),
                AutoRoute(page: UnscheduledRoute.page),
                AutoRoute(page: PosListRoute.page),
                AutoRoute(page: CompletedTasksRoute.page),
                AutoRoute(page: ScheduleRoute.page),
                AutoRoute(page: NotificationsRoute.page),
                AutoRoute(page: TodayRoute.page),
                AutoRoute(page: JourneyMapRoute.page),
                <PERSON>Route(page: TaskDetailsRoute.page),
                AutoRoute(page: PosRoute.page),
                AutoRoute(page: NotesRoute.page),
                AutoRoute(page: StoreHistoryRoute.page),
                AutoRoute(page: StoreHistoryItemsRoute.page),
                AutoRoute(page: PreviousTaskFormRoute.page),
                AutoRoute(page: StoreInfoRoute.page),
                AutoRoute(page: FormRoute.page),
                AutoRoute(page: QuestionRoute.page),
                AutoRoute(page: QPMDRoute.page),
                AutoRoute(page: FlipRoute.page),
                AutoRoute(page: FQPDRoute.page),
                AutoRoute(page: SubHeaderRoute.page),
                AutoRoute(page: SignatureRoute.page),
                AutoRoute(page: MPTRoute.page),
                AutoRoute(page: AddContactRoute.page),
                AutoRoute(page: AddStoreCommentRoute.page),
                AutoRoute(page: AutoScheduleRoute.page),
                AutoRoute(page: VacanciesRoute.page),
                AutoRoute(page: ReferralFormRoute.page),
                AutoRoute(page: OpenTasksRoute.page),
                AutoRoute(page: DashboardHistoryRoute.page),
                AutoRoute(page: EmulateUserRoute.page),
                AutoRoute(page: CreateTaskRoute.page),
                AutoRoute(page: TaskFilesRoute.page),
              ],
            ),
            AutoRoute(page: AssistantRoute.page),
            AutoRoute(
              page: ProfileHolderRoute.page,
              children: [
                AutoRoute(
                  page: ProfileRoute.page,
                  initial: true,
                ),
                AutoRoute(page: EditProfileRoute.page),
                AutoRoute(page: AvailabilityRoute.page),
                AutoRoute(page: SkillsRoute.page),
                AutoRoute(page: LeaveRoute.page),
                AutoRoute(page: AddLeaveRoute.page),
                AutoRoute(page: InductionRoute.page),
                AutoRoute(page: ProfileHistoryRoute.page),
              ],
            ),
            AutoRoute(
              page: MoreHolderRoute.page,
              children: [
                AutoRoute(
                  page: MoreRoute.page,
                  initial: true,
                ),
                AutoRoute(page: UsefulLinksRoute.page),
              ],
            ),
          ],
        ),
        AutoRoute(
          page: WebBrowserRoute.page,
        ),
        AutoRoute(
          page: TutorialRoute.page,
        ),
        AutoRoute(
          page: BarcodeScannerRoute.page,
        ),
      ];
}
