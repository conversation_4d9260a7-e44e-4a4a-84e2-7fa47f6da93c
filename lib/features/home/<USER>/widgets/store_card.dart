import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/gestures.dart';
import 'package:gap/gap.dart';
import 'package:intl/intl.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:auto_route/auto_route.dart';
import 'package:storetrack_app/shared/widgets/offline_image_widget.dart';
import '../../../../config/themes/app_colors.dart';
import '../../../../config/routes/app_router.gr.dart';
import '../../../../core/constants/app_assets.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/utils/time_access_validator.dart';
import '../../../../shared/widgets/comments.dart';
import '../constants/action_types.dart';
import '../widgets/task_action_button.dart';
import 'task_card.dart';

class StoreCard extends StatefulWidget {
  final List<TaskDetail> task;
  final String heading;
  final List<Widget> parentActions;
  final List<Widget> subTaskActions;
  final bool isCalendarMode;
  final bool showScheduledDate;
  final bool showTickIndicator;
  final bool showAllDisclosureIndicator;
  final bool permanentlyDisableAllDisclosureIndicator;
  final bool isOpenTask;
  final bool initialParentCheckboxValue;
  final Map<String, bool> initialChildCheckboxStates;
  final void Function(int oldIndex, int newIndex)? onSubtaskReorder;
  final void Function(bool isSelected, TaskDetail task)?
      onParentSelectionChanged;
  final void Function(bool isSelected, TaskDetail task)?
      onSubtaskSelectionChanged;
  final void Function(String actionType, TaskDetail task)? onSubtaskActionTap;
  final void Function(TaskDetail task)? onTaskTap;
  final bool enableTimeValidation;
  final bool disableTaskNavigation;

  const StoreCard({
    super.key,
    required this.task,
    required this.parentActions,
    required this.subTaskActions,
    this.isCalendarMode = false,
    this.showScheduledDate = false,
    this.showTickIndicator = false,
    this.showAllDisclosureIndicator = false,
    this.permanentlyDisableAllDisclosureIndicator = false,
    this.isOpenTask = false,
    this.initialParentCheckboxValue = false,
    this.initialChildCheckboxStates = const {},
    this.onSubtaskReorder,
    this.onParentSelectionChanged,
    this.onSubtaskSelectionChanged,
    this.onSubtaskActionTap,
    this.onTaskTap,
    this.enableTimeValidation = false,
    this.disableTaskNavigation = false,
    required this.heading,
  });

  @override
  State<StoreCard> createState() => _StoreCardState();
}

class _StoreCardState extends State<StoreCard>
    with SingleTickerProviderStateMixin {
  // State for parent checkbox
  bool _isChecked = false;

  // Map to track the state of each child checkbox
  final Map<String, bool> _childCheckboxStates = {};

  // Controllers for the reorderable lists
  late final ScrollController _calendarModeScrollController;
  late final ScrollController _nonCalendarModeScrollController;

  // State to track if the card is expanded or collapsed
  bool _isExpanded = false;

  // Animation controller for smooth expansion/collapse
  late AnimationController _expandController;
  late Animation<double> _expandAnimation;

  // Generate a consistent color based on the store name
  Color _getColorFromName(String name) {
    if (name.isEmpty) return Colors.blueAccent.shade700;

    // Use the sum of character codes to generate a consistent hue
    int sum = 0;
    for (int i = 0; i < name.length; i++) {
      sum += name.codeUnitAt(i);
    }

    // Use the sum to generate a hue value between 0 and 360
    final hue = (sum % 360).toDouble();

    // Create a color with full saturation but lower brightness for darker colors
    return HSVColor.fromAHSV(1.0, hue, 0.85, 0.65).toColor();
  }

  @override
  void initState() {
    super.initState();
    // Initialize checkbox states from props
    _isChecked = widget.initialParentCheckboxValue;
    _initializeChildCheckboxStates();

    // Initialize scroll controllers
    _calendarModeScrollController = ScrollController();
    _nonCalendarModeScrollController = ScrollController();

    // Initialize animation controller
    _expandController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    // Create animation with curve for smoother effect
    _expandAnimation = CurvedAnimation(
      parent: _expandController,
      curve: Curves.easeInOut,
    );
  }

  @override
  void didUpdateWidget(StoreCard oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Update parent checkbox state if it changed
    if (oldWidget.initialParentCheckboxValue !=
        widget.initialParentCheckboxValue) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        setState(() {
          _isChecked = widget.initialParentCheckboxValue;
        });
      });
    }

    // Update child checkbox states if tasks or initialChildCheckboxStates changed
    if (oldWidget.task != widget.task ||
        oldWidget.initialChildCheckboxStates !=
            widget.initialChildCheckboxStates) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _initializeChildCheckboxStates();
      });
    }
  }

  @override
  void dispose() {
    // Clean up controllers
    _calendarModeScrollController.dispose();
    _nonCalendarModeScrollController.dispose();
    _expandController.dispose();
    super.dispose();
  }

  void _initializeChildCheckboxStates() {
    // Clear existing states
    _childCheckboxStates.clear();

    // Initialize states from passed values or default to false
    for (var task in widget.task) {
      final taskId = task.taskId.toString();
      _childCheckboxStates[taskId] =
          widget.initialChildCheckboxStates[taskId] ?? false;
    }

    // If we're not using passed parent checkbox value, update it based on children
    if (!widget.initialParentCheckboxValue) {
      // Update parent checkbox based on children's state
      if (_childCheckboxStates.isEmpty) {
        _isChecked = false;
      } else {
        // If all children are checked, parent is checked
        _isChecked =
            _childCheckboxStates.values.every((state) => state == true);
      }
    }
  }

  // Handle parent checkbox change - This is user interaction so immediate setState is fine
  void _handleParentCheckboxChanged(bool? value) {
    final bool valueToUse = value ?? false;

    // If selecting, select parent and all children
    // If deselecting, deselect parent and all children
    setState(() {
      _isChecked = valueToUse;

      // Update all children to match parent
      for (var key in _childCheckboxStates.keys) {
        _childCheckboxStates[key] = valueToUse;
      }
    });

    // Notify parent widget about selection change
    if (widget.onParentSelectionChanged != null && widget.task.isNotEmpty) {
      widget.onParentSelectionChanged!(valueToUse, widget.task.first);
    }
  }

  // Handle child checkbox change - This is user interaction so immediate setState is fine
  void _handleChildCheckboxChanged(String taskId, bool value) {
    setState(() {
      // Update the specific child checkbox
      _childCheckboxStates[taskId] = value;

      // Update parent checkbox state based on children
      if (_childCheckboxStates.isNotEmpty) {
        // If all children are checked, parent is checked
        _isChecked =
            _childCheckboxStates.values.every((state) => state == true);
      } else {
        _isChecked = false;
      }
    });

    // Find the task with this ID
    final selectedTask = widget.task.firstWhere(
      (task) => task.taskId.toString() == taskId,
      orElse: () => TaskDetail(),
    );

    // Notify parent widget about selection change
    if (widget.onSubtaskSelectionChanged != null) {
      widget.onSubtaskSelectionChanged!(value, selectedTask);
    }
  }

  // Toggle expanded/collapsed state
  void _toggleExpanded() {
    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _expandController.forward();
      } else {
        _expandController.reverse();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // For both modes single widget is enough
        return Padding(
          padding: const EdgeInsets.symmetric(
            vertical: 0,
            horizontal: 16,
          ),
          child: TaskCard(
            actions: widget.parentActions,
            isCalendarMode: widget.isCalendarMode,
            borderRadius: AppConstants.defaultBorderRadius,
            // border: Border.all(color: AppColors.borderColor),
            backgroundColor: Colors.white,
            // Only pass checkbox properties if in calendar mode
            initialCheckboxValue: widget.isCalendarMode ? _isChecked : false,
            onCheckboxChanged:
                widget.isCalendarMode ? _handleParentCheckboxChanged : null,
            actionSpacing: 8.0, // Spacing between action buttons
            showScheduledDate: widget.showScheduledDate,
            showTickIndicator: widget.showTickIndicator,
            showAllDisclosureIndicator: widget.showAllDisclosureIndicator,
            permanentlyDisableAllDisclosureIndicator:
                widget.permanentlyDisableAllDisclosureIndicator,
            isOpenTask: widget.isOpenTask,
            actionsAlignment:
                MainAxisAlignment.start, // Align parent actions to the top
            isParentCard: true, // This is a parent card
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Parent card header (COLES) - Only this area should be tappable for expand/minimize
                GestureDetector(
                  onTap: _toggleExpanded,
                  child: Padding(
                    padding: EdgeInsets.only(bottom: _isExpanded ? 12 : 4),
                    child: _buildParentTaskHeaderContent(),
                  ),
                ),

                // Subtasks section (if any exist and card is expanded)
                if (widget.task.isNotEmpty)
                  AnimatedBuilder(
                    animation: _expandAnimation,
                    builder: (context, child) {
                      return ClipRect(
                        child: Align(
                          heightFactor: _expandAnimation.value,
                          child: child,
                        ),
                      );
                    },
                    child: Padding(
                      padding:
                          const EdgeInsets.only(left: 0, right: 0, bottom: 0),
                      child: AbsorbPointer(
                        absorbing:
                            !_isExpanded, // Only allow interactions when expanded
                        child: Container(
                          constraints: const BoxConstraints(minHeight: 0),
                          clipBehavior: Clip.hardEdge, // Clip at the boundaries
                          decoration: BoxDecoration(
                            // Add a container to constrain the dragging area
                            border: Border.all(color: Colors.transparent),
                            borderRadius: BorderRadius.circular(
                                AppConstants.defaultBorderRadius),
                          ),
                          child: ReorderableListView(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            padding: EdgeInsets.zero,
                            // Disable dragging outside the container
                            dragStartBehavior: DragStartBehavior.down,
                            // Use the non-calendar mode scroll controller
                            scrollController: _nonCalendarModeScrollController,
                            // Add proxyDecorator to fix light red background during dragging
                            proxyDecorator: (child, index, animation) {
                              return Material(
                                elevation: 3 * animation.value,
                                color: Colors.transparent,
                                shadowColor:
                                    Colors.black.withValues(alpha: 0.15),
                                borderRadius: BorderRadius.circular(8),
                                child: child,
                              );
                            },
                            onReorderStart: (index) {
                              HapticFeedback.mediumImpact();
                            },
                            onReorder: (oldIndex, newIndex) {
                              // Ensure reordering only happens within this list
                              if (oldIndex < 0 ||
                                  oldIndex >= widget.task.length ||
                                  newIndex < 0 ||
                                  newIndex > widget.task.length) {
                                return;
                              }

                              // Provide haptic feedback for successful reorder
                              HapticFeedback.mediumImpact();

                              // Call the parent's reorder function if provided
                              if (widget.onSubtaskReorder != null) {
                                widget.onSubtaskReorder!(oldIndex, newIndex);
                              }
                            },
                            // Ensure there's at least one item or provide a placeholder if empty
                            children: widget.task.isEmpty
                                ? [
                                    Container(
                                      key: const ValueKey('empty_placeholder'),
                                      height: 1,
                                      width: double.infinity,
                                      color: Colors.transparent,
                                    )
                                  ]
                                : [
                                    for (int index = 0;
                                        index < widget.task.length;
                                        index++)
                                      if (widget.task[index].taskId !=
                                          null) // Only add items with valid IDs
                                        TaskCard(
                                          key: ValueKey(
                                              'subtask_${widget.task[index].taskId}_$index'),
                                          actions: _createSubtaskActions(
                                              widget.task[index]),
                                          borderRadius:
                                              AppConstants.defaultBorderRadius,
                                          border: Border.all(
                                              color: AppColors.borderBlack),
                                          backgroundColor: Colors.white,
                                          content: _buildSubTaskContent(
                                              widget.task[index]),
                                          isCalendarMode: widget.isCalendarMode,
                                          // Only pass checkbox properties if in calendar mode
                                          initialCheckboxValue:
                                              widget.isCalendarMode
                                                  ? (_childCheckboxStates[widget
                                                          .task[index].taskId
                                                          .toString()] ??
                                                      false)
                                                  : false,
                                          onCheckboxChanged: widget
                                                  .isCalendarMode
                                              ? (value) {
                                                  _handleChildCheckboxChanged(
                                                      widget.task[index].taskId
                                                          .toString(),
                                                      value);
                                                }
                                              : null,
                                          actionSpacing:
                                              8.0, // Spacing between action buttons
                                          showScheduledDate:
                                              widget.showScheduledDate,
                                          showTickIndicator:
                                              widget.showTickIndicator,
                                          showAllDisclosureIndicator:
                                              widget.showAllDisclosureIndicator,
                                          permanentlyDisableAllDisclosureIndicator:
                                              widget
                                                  .permanentlyDisableAllDisclosureIndicator,
                                          isOpenTask: widget.isOpenTask,
                                          actionsAlignment: MainAxisAlignment
                                              .center, // Keep child actions centered
                                          isParentCard:
                                              false, // This is a child card
                                        ),
                                  ],
                          ),
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
        );
      },
    );
  }

  // Builds content for parent header (COLES) - NO CHANGES NEEDED HERE
  Widget _buildParentTaskHeaderContent() {
    var textTheme = Theme.of(context).textTheme;

    // Make sure we have at least one task
    if (widget.task.isEmpty) {
      return const SizedBox.shrink();
    }

    var task = widget.task[0];
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Text(text),
        // SizedBox(height: 16,),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Flexible(
              // Prevent overflow if company name is very long
              child: Row(
                mainAxisSize:
                    MainAxisSize.min, // Don't take full width unnecessarily
                children: [
                  Container(
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: Colors.grey.withValues(alpha: 0.3),
                        width: 1,
                      ),
                    ),
                    child: CircleAvatar(
                      radius: 16,
                      backgroundColor: task.clientLogoUrl != null &&
                              task.clientLogoUrl!.isNotEmpty
                          ? Colors.transparent
                          : _getColorFromName(task.storeName ?? ''),
                      child: task.clientLogoUrl != null &&
                              task.clientLogoUrl!.isNotEmpty
                          ? ClipOval(
                              child: OfflineImageWidget(
                                url: task.clientLogoUrl!,
                                width: 32,
                                height: 32,
                                fit: BoxFit.cover,
                                borderRadius: 0,
                              ),
                            )
                          : Text(
                              (task.storeName?.length ?? 0) >= 2
                                  ? task.storeName!
                                      .substring(0, 2)
                                      .toUpperCase()
                                  : task.storeName ?? '',
                              style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 12,
                              ),
                            ),
                    ),
                  ),
                  // CircleAvatar(
                  //   radius: 16,
                  //   backgroundColor: _getColorFromName(task.storeName ?? ''),
                  //   child: Text(
                  //     (task.storeName?.length ?? 0) >= 2
                  //         ? task.storeName!.substring(0, 2).toUpperCase()
                  //         : task.storeName ?? '',
                  //     style: const TextStyle(
                  //       color: Colors.white,
                  //       fontWeight: FontWeight.bold,
                  //       fontSize: 12,
                  //     ),
                  //   ),
                  // ),
                  const SizedBox(width: AppConstants.smallPadding),
                  Flexible(
                    // Allow text to wrap or shrink if needed
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              flex: 8,
                              child: Text(
                                task.storeName ?? "",
                                style: textTheme.montserratTitleExtraSmall,
                                overflow:
                                    TextOverflow.ellipsis, // Handle long names
                                maxLines: 2,
                              ),
                            ),
                            const Spacer(),
                            const Icon(
                              Icons.access_time,
                              size: 16,
                              color: AppColors.blackTint1,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              '${widget.task.isEmpty ? 0 : widget.task.map((e) => e.budget ?? 0).reduce((a, b) => (a + b))}m',
                              style:
                                  textTheme.montserratTitleExtraSmall.copyWith(
                                color: AppColors.blackTint1,
                              ),
                            ),
                          ],
                        ),
                        if (task.location != null)
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Icon(
                                Icons.location_on_outlined,
                                size: 16,
                                color: AppColors.black,
                              ),
                              const SizedBox(width: 2),
                              Flexible(
                                // Allow location to wrap/ellipsis
                                child: Text(
                                  task.location ?? "",
                                  style: textTheme.montserratTableSmall,
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 2,
                                ),
                              ),
                            ],
                          ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            // const Gap(4),
            // Keep duration on the right
          ],
        ),
      ],
    );
  }

  // Create subtask actions with the specific subtask
  List<Widget> _createSubtaskActions(TaskDetail subtask) {
    // If we're in checkbox mode, return the original actions
    if (widget.isCalendarMode) {
      return widget.subTaskActions;
    }

    // Otherwise, create new actions with the specific subtask
    List<Widget> actions = [];

    // enable view brief if task.hasBrief == true
    // enable change helper if taskCount > 1 and teamlead == 1
    // enable view pos if subTask.posRequired == true and permamentlyDisableAllDisclosureIndicator == false
    // enable view note if subTask.taskNote != null or subTask.notes != ""
    // enable chat document if permamentlyDisableAllDisclosureIndicator == false

    for (var action in widget.subTaskActions) {
      if (action is TaskActionButton) {
        // Check action type instead of icon
        if (action.actionType == ActionTypes.viewBrief) {
          // Only include the view_brief button if the task has a brief
          bool hasBrief = subtask.forms != null &&
              subtask.forms!.isNotEmpty &&
              subtask.forms!.any((form) =>
                  form.questions != null &&
                  form.questions!.isNotEmpty &&
                  form.questions!.any((question) =>
                      question.questionBrief?.isNotEmpty == true));

          if (hasBrief) {
            // Clone the action button but update its onPressed callback
            actions.add(TaskActionButton(
              icon: action.icon,
              isPrimary: action.isPrimary,
              actionType: ActionTypes.viewBrief,
              onPressed: () {
                // This is a view_brief action, so we need to pass the specific subtask
                if (widget.onSubtaskActionTap != null) {
                  widget.onSubtaskActionTap!(ActionTypes.viewBrief, subtask);
                } else if (action.onPressed != null) {
                  // Fallback to the original action's onPressed
                  action.onPressed!();
                }
              },
            ));
          }
        } else if (action.actionType == ActionTypes.changeHelper) {
          // Only include change_helper if taskCount > 1 and teamlead == 1
          bool enableChangeHelper =
              (subtask.taskCount ?? 0) > 1 && subtask.teamlead == 1;

          if (enableChangeHelper) {
            actions.add(TaskActionButton(
              icon: action.icon,
              isPrimary: action.isPrimary,
              actionType: ActionTypes.changeHelper,
              onPressed: () {
                if (widget.onSubtaskActionTap != null) {
                  widget.onSubtaskActionTap!(ActionTypes.changeHelper, subtask);
                } else if (action.onPressed != null) {
                  action.onPressed!();
                }
              },
            ));
          }
        } else if (action.actionType == ActionTypes.viewPos) {
          // VIEW POS: Only include if posRequired is true and permanentlyDisableAllDisclosureIndicator is false
          bool enableViewPos = (subtask.posRequired == true) &&
              !widget.permanentlyDisableAllDisclosureIndicator;

          if (enableViewPos) {
            actions.add(TaskActionButton(
              icon: action.icon,
              isPrimary: action.isPrimary,
              actionType: ActionTypes.viewPos,
              onPressed: () {
                if (widget.onSubtaskActionTap != null) {
                  widget.onSubtaskActionTap!(ActionTypes.viewPos, subtask);
                } else if (action.onPressed != null) {
                  action.onPressed!();
                }
              },
            ));
          }
        } else if (action.actionType == ActionTypes.viewNote) {
          // VIEW NOTE: Only include if taskNote is not null or notes is not empty
          bool enableViewNote =
              (subtask.taskNote != null && subtask.taskNote!.isNotEmpty) ||
                  (subtask.comment != null && subtask.comment!.isNotEmpty);

          if (enableViewNote) {
            actions.add(TaskActionButton(
              icon: action.icon,
              isPrimary: action.isPrimary,
              actionType: ActionTypes.viewNote,
              onPressed: () {
                if (widget.onSubtaskActionTap != null) {
                  widget.onSubtaskActionTap!(ActionTypes.viewNote, subtask);
                } else if (action.onPressed != null) {
                  action.onPressed!();
                }
              },
            ));
          }
        } else if (action.actionType == ActionTypes.viewDocument) {
          // VIEW DOCUMENT: Only include if permanentlyDisableAllDisclosureIndicator is false
          bool enableViewDocument =
              !widget.permanentlyDisableAllDisclosureIndicator;

          if (enableViewDocument) {
            actions.add(TaskActionButton(
              icon: action.icon,
              isPrimary: action.isPrimary,
              actionType: ActionTypes.viewDocument,
              onPressed: () {
                if (widget.onSubtaskActionTap != null) {
                  widget.onSubtaskActionTap!(ActionTypes.viewDocument, subtask);
                } else if (action.onPressed != null) {
                  action.onPressed!();
                }
              },
            ));
          }
        } else if (action.actionType == ActionTypes.chatAssistant) {
          // CHAT ASSISTANT: Only include if permanentlyDisableAllDisclosureIndicator is false
          bool enableChatAssistant =
              !widget.permanentlyDisableAllDisclosureIndicator;

          if (enableChatAssistant) {
            actions.add(TaskActionButton(
              icon: action.icon,
              isPrimary: action.isPrimary,
              actionType: ActionTypes.chatAssistant,
              onPressed: () {
                if (widget.onSubtaskActionTap != null) {
                  widget.onSubtaskActionTap!(
                      ActionTypes.chatAssistant, subtask);
                } else if (action.onPressed != null) {
                  action.onPressed!();
                }
              },
            ));
          }
        } else {
          // For any other action types, include them normally
          actions.add(TaskActionButton(
            icon: action.icon,
            isPrimary: action.isPrimary,
            actionType: action.actionType,
            onPressed: () {
              if (action.onPressed != null) {
                action.onPressed!();
              }
            },
          ));
        }
      } else {
        actions.add(action);
      }
    }

    return actions;
  }

  // Builds content for subtask cards (PEPSI CO, SANOFI)
  Widget _buildSubTaskContent(TaskDetail subtask) {
    var textTheme = Theme.of(context).textTheme;
    // Add padding inside the subtask content card
    return GestureDetector(
      onTap: () {
        // Navigate to task detail page when subtask content is tapped
        if (_isExpanded) {
          // Call the callback if provided
          widget.onTaskTap?.call(subtask);
        }
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // First row: Company name and duration
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                // Allow name to take space but prevent overflow
                child: Text(
                  subtask.client ?? "",
                  style: textTheme.montserratTitleExtraSmall,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
              ),
              const Gap(4),
              Row(
                mainAxisSize: MainAxisSize.min, // Don't expand unnecessarily
                children: [
                  const Icon(
                    Icons.access_time,
                    size: 16,
                    color: AppColors.blackTint1,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${subtask.budget}m',
                    style: textTheme.montserratTitleExtraSmall.copyWith(
                      color: AppColors.blackTint1,
                    ),
                  ),
                ],
              ),
              const SizedBox(width: 8),
              GestureDetector(
                onTap: widget.disableTaskNavigation
                    ? null
                    : () async {
                        if (widget.enableTimeValidation) {
                          // Use time validation - follow the same pattern as scheduled_page.dart
                          if ((await TimeAccessValidator.canAccessTask(
                                  context, subtask) ||
                              subtask.reOpened == true)) {
                            if (mounted) {
                              context.router.push(TaskDetailsRoute(
                                taskId: subtask.taskId!.toInt(),
                                storeId: subtask.storeId ?? 0,
                              ));
                            }
                          }
                        } else {
                          // Direct navigation without validation (current behavior)
                          context.router.push(TaskDetailsRoute(
                            taskId: subtask.taskId!.toInt(),
                            storeId: subtask.storeId ?? 0,
                          ));
                        }
                      },
                child: Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: widget.disableTaskNavigation
                        ? AppColors.midGrey
                        : AppColors.primaryBlue,
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.arrow_forward_ios,
                    color: widget.disableTaskNavigation
                        ? AppColors.blackTint1
                        : Colors.white,
                    size: 12,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),

          // Second row: Task details
          Text(
            subtask.cycle ?? "",
            style: textTheme.montserratTableSmall,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 4),

          // Third row: Date range
          Row(
            children: [
              Image.asset(
                AppAssets.notificationLocation,
                scale: 5,
                color: Colors.black,
              ),
              const SizedBox(width: 4),
              Expanded(
                // Allow date range to wrap/ellipsis if very long
                child: Text(
                  '${subtask.rangeStart != null ? DateFormat('dd/MM/yyyy').format(subtask.rangeStart!) : ""} - ${subtask.rangeEnd != null ? DateFormat('dd/MM/yyyy').format(subtask.rangeEnd!) : ""}',
                  style: textTheme.montserratTableSmall,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),

          // Fourth row: Duration
          Row(
            children: [
              const Icon(
                Icons.access_time,
                size: 16,
                color: Colors.black,
              ),
              const SizedBox(width: 4),
              Text(
                '${subtask.budget ?? 0}m',
                style: textTheme.montserratTableSmall,
              ),
            ],
          ),
          const SizedBox(height: 4),

          // Fifth row: Scheduled date
          Row(
            children: [
              const Icon(
                Icons.calendar_today,
                size: 16,
                color: Colors.black,
              ),
              const SizedBox(width: 4),
              Text(
                subtask.scheduledTimeStamp != null &&
                        subtask.scheduledTimeStamp!
                            .isAfter(AppConstants.minDateTime)
                    ? DateFormat('dd/MM/yyyy')
                        .format(subtask.scheduledTimeStamp!)
                    : 'Not Scheduled',
                style: textTheme.montserratTableSmall,
              ),
            ],
          ),
          const SizedBox(height: 4),

          // Sixth row: Assigned users
          Row(
            children: [
              Image.asset(
                AppAssets.homeProfile,
                scale: 6,
                color: Colors.black,
              ),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  // loop and join
                  subtask.taskmembers == null || subtask.taskmembers!.isEmpty
                      ? 'N/A'
                      : subtask.taskmembers!
                          .map((member) => member.fullname)
                          .join(', '),
                  style: textTheme.montserratTableSmall,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),

          // Seventh row (conditional): Task count and status
          if ((subtask.taskCount ?? 0) > 0 || subtask.taskStatus != null)
            Padding(
              padding: const EdgeInsets.only(top: 4.0),
              child: Row(
                children: [
                  if ((subtask.taskCount ?? 0) >
                      0) // Show icon only if count > 0
                    Image.asset(
                      AppAssets.posIcon,
                      scale: 8,
                      color: Colors.black,
                    ),
                  if ((subtask.taskCount ?? 0) > 0) const SizedBox(width: 4),
                  if ((subtask.taskCount ?? 0) > 0)
                    Text(
                      '${subtask.taskCount}',
                      style: textTheme.montserratTableSmall,
                    ),
                  if ((subtask.taskCount ?? 0) > 0 &&
                      subtask.taskStatus != null)
                    const SizedBox(width: 8), // Spacer only if both exist
                  if (subtask.taskStatus != null)
                    Expanded(
                      // Allow status to take remaining space and ellipsis
                      child: Text(
                        subtask.taskStatus ?? "",
                        style: textTheme.montserratTableSmall.copyWith(
                          fontStyle: FontStyle.italic,
                          color: AppColors.primaryBlue,
                        ),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                    ),
                ],
              ),
            ),

          // Warning notes (conditional)
          if (subtask.taskalerts != null && subtask.taskalerts!.isNotEmpty)
            const SizedBox(height: 8),
          if (subtask.taskalerts != null && subtask.taskalerts!.isNotEmpty)
            UrgentCommentWidget(
              text:
                  (subtask.taskalerts != null && subtask.taskalerts!.isNotEmpty)
                      ? (subtask.taskalerts![0].message ?? "")
                      : "",
            ),
        ],
      ),
    );
  }
}
