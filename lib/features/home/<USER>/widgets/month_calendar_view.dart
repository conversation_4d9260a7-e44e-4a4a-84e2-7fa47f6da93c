import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as schedule;
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/reorderable_store_list.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/empty_state.dart';
import 'package:gap/gap.dart';

class MonthCalendarView extends StatefulWidget {
  final List<schedule.TaskDetail> allApiTasks;
  final bool isCheckboxMode;
  final bool areAllItemsSelected;
  final Function(List<TaskDetail>, List<TaskDetail>) onSelectionChanged;
  final TaskDetail Function(schedule.TaskDetail) convertScheduleToDatum;
  final String Function(List<schedule.TaskDetail>) calculateTotalHours;
  final void Function(TaskDetail task)? onTaskTap;

  const MonthCalendarView({
    super.key,
    required this.allApiTasks,
    required this.isCheckboxMode,
    required this.areAllItemsSelected,
    required this.onSelectionChanged,
    required this.convertScheduleToDatum,
    required this.calculateTotalHours,
    this.onTaskTap,
  });

  @override
  State<MonthCalendarView> createState() => _MonthCalendarViewState();
}

class _MonthCalendarViewState extends State<MonthCalendarView> {
  late DateTime _focusedDay;
  DateTime? _selectedDay;
  late CalendarFormat _calendarFormat;

  @override
  void initState() {
    super.initState();
    _focusedDay = DateTime.now();
    _selectedDay = DateTime.now();
    _calendarFormat = CalendarFormat.month;
  }

  Map<String, String> _getTaskHoursForDates() {
    Map<String, List<schedule.TaskDetail>> tasksByDate = {};

    for (var task in widget.allApiTasks) {
      if (task.taskStatus != "Confirmed" ||
          task.isOpen == true ||
          task.scheduledTimeStamp == null) {
        continue;
      }

      final taskDate = task.scheduledTimeStamp!;
      final dateKey = "${taskDate.year}-${taskDate.month}-${taskDate.day}";

      if (!tasksByDate.containsKey(dateKey)) {
        tasksByDate[dateKey] = [];
      }
      tasksByDate[dateKey]!.add(task);
    }

    Map<String, String> taskHours = {};
    tasksByDate.forEach((dateKey, tasks) {
      final totalHours = widget.calculateTotalHours(tasks);
      taskHours[dateKey] = totalHours;
    });

    return taskHours;
  }

  List<schedule.TaskDetail> _getTasksForSelectedDate() {
    if (_selectedDay == null) return [];

    List<schedule.TaskDetail> tasksForDate = [];
    final selectedDateOnly =
        DateTime(_selectedDay!.year, _selectedDay!.month, _selectedDay!.day);

    for (var task in widget.allApiTasks) {
      if (task.taskStatus != "Confirmed" ||
          task.isOpen == true ||
          task.scheduledTimeStamp == null) {
        continue;
      }

      final taskDate = DateTime(task.scheduledTimeStamp!.year,
          task.scheduledTimeStamp!.month, task.scheduledTimeStamp!.day);

      if (taskDate.isAtSameMomentAs(selectedDateOnly)) {
        tasksForDate.add(task);
      }
    }

    return tasksForDate;
  }

  Map<DateTime, List<schedule.TaskDetail>> _getTasksGroupedByDate() {
    Map<DateTime, List<schedule.TaskDetail>> tasksByDate = {};

    for (var task in widget.allApiTasks) {
      if (task.taskStatus != "Confirmed" ||
          task.isOpen == true ||
          task.scheduledTimeStamp == null) {
        continue;
      }

      final taskDate = DateTime(task.scheduledTimeStamp!.year,
          task.scheduledTimeStamp!.month, task.scheduledTimeStamp!.day);

      if (!tasksByDate.containsKey(taskDate)) {
        tasksByDate[taskDate] = [];
      }
      tasksByDate[taskDate]!.add(task);
    }

    return tasksByDate;
  }

  @override
  Widget build(BuildContext context) {
    final taskHours = _getTaskHoursForDates();
    final tasksForSelectedDate = _getTasksForSelectedDate();
    final convertedTasks = tasksForSelectedDate
        .map((task) => widget.convertScheduleToDatum(task))
        .toList();
    final allTasksByDate = _getTasksGroupedByDate();

    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Calendar
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(0),
            ),
            child: TableCalendar<schedule.TaskDetail>(
              firstDay: DateTime.utc(2020, 1, 1),
              lastDay: DateTime.utc(2030, 12, 31),
              focusedDay: _focusedDay,
              calendarFormat: _calendarFormat,
              startingDayOfWeek: StartingDayOfWeek.sunday,
              availableGestures: AvailableGestures.none,
              headerStyle: HeaderStyle(
                titleCentered: true,
                formatButtonVisible: false,
                titleTextStyle:
                    Theme.of(context).textTheme.montserratTitleSmall,
                leftChevronIcon: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(Icons.chevron_left),
                ),
                rightChevronIcon: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(Icons.chevron_right),
                ),
                headerPadding: const EdgeInsets.symmetric(vertical: 16),
              ),
              daysOfWeekStyle: DaysOfWeekStyle(
                weekdayStyle:
                    Theme.of(context).textTheme.montserratMetricsAxisRegular,
                weekendStyle:
                    Theme.of(context).textTheme.montserratMetricsAxisRegular,
              ),
              calendarStyle: CalendarStyle(
                outsideDaysVisible: true,
                defaultTextStyle:
                    Theme.of(context).textTheme.montserratTableSmall,
                weekendTextStyle:
                    Theme.of(context).textTheme.montserratTableSmall,
                outsideTextStyle:
                    Theme.of(context).textTheme.montserratTableSmall.copyWith(
                          color: Colors.grey.shade400,
                        ),
                todayDecoration: const BoxDecoration(
                  shape: BoxShape.circle,
                  color: AppColors.primaryBlue,
                ),
                todayTextStyle:
                    Theme.of(context).textTheme.montserratTableSmall.copyWith(
                          fontWeight: FontWeight.w500,
                          color: Colors.white,
                        ),
                selectedDecoration: const BoxDecoration(
                  shape: BoxShape.circle,
                  color: AppColors.primaryBlue,
                ),
                selectedTextStyle:
                    Theme.of(context).textTheme.montserratTableSmall.copyWith(
                          fontWeight: FontWeight.w500,
                          color: Colors.white,
                        ),
                cellMargin: const EdgeInsets.all(4),
              ),
              selectedDayPredicate: (day) {
                return _selectedDay != null && isSameDay(_selectedDay, day);
              },
              onDaySelected: (selectedDay, focusedDay) {
                setState(() {
                  _selectedDay = selectedDay;
                  _focusedDay = focusedDay;
                });
              },
              onPageChanged: (focusedDay) {
                _focusedDay = focusedDay;
              },
              eventLoader: (day) {
                return allTasksByDate[DateTime(day.year, day.month, day.day)] ??
                    [];
              },
              calendarBuilders: CalendarBuilders(
                markerBuilder: (context, date, events) {
                  final dateKey = "${date.year}-${date.month}-${date.day}";
                  final hours = taskHours[dateKey];

                  if (hours != null && events.isNotEmpty) {
                    return Positioned(
                      bottom: 1,
                      right: 1,
                      child: Container(
                        padding: const EdgeInsets.all(1),
                        child: Text(
                          hours.replaceAll(' hrs', ''),
                          style: Theme.of(context)
                              .textTheme
                              .montserratTableSmall
                              .copyWith(
                                fontSize: 8,
                                fontWeight: FontWeight.w500,
                                color: Colors.grey.shade600,
                              ),
                        ),
                      ),
                    );
                  }

                  return null;
                },
              ),
            ),
          ),

          const Gap(16),

          // Tasks for selected date or all tasks grouped by date
          _selectedDay != null && tasksForSelectedDate.isNotEmpty
              ? _buildTasksForSelectedDate(tasksForSelectedDate, convertedTasks)
              : _buildAllTasksGroupedByDate(allTasksByDate),
        ],
      ),
    );
  }

  Widget _buildTasksForSelectedDate(
    List<schedule.TaskDetail> tasksForSelectedDate,
    List<TaskDetail> convertedTasks,
  ) {
    return Column(
      children: [
        // Date header
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                DateFormat('EEE d MMM').format(_selectedDay!),
                style: Theme.of(context).textTheme.montserratTitleSmall,
              ),
              Text(
                widget.calculateTotalHours(tasksForSelectedDate),
                style: Theme.of(context)
                    .textTheme
                    .montserratTitleExtraSmall
                    .copyWith(color: Colors.black),
              ),
            ],
          ),
        ),

        // Task list
        ReorderableStoreList(
          tasks: convertedTasks,
          isCalendarMode: widget.isCheckboxMode,
          showScheduledDate: false,
          showTickIndicator: true,
          showAllDisclosureIndicator: false,
          permanentlyDisableAllDisclosureIndicator: false,
          isOpenTask: true,
          onSelectionChanged: widget.onSelectionChanged,
          selectAll: widget.areAllItemsSelected,
          onTaskTap: widget.onTaskTap,
          enableTimeValidation: true,
        ),

        // Add bottom padding when in checkbox mode
        if (widget.isCheckboxMode) const SizedBox(height: 120),
      ],
    );
  }

  Widget _buildAllTasksGroupedByDate(
      Map<DateTime, List<schedule.TaskDetail>> allTasksByDate) {
    if (allTasksByDate.isEmpty) {
      return const EmptyState(message: 'No tasks for this month');
    }

    // Sort dates
    List<DateTime> sortedDates = allTasksByDate.keys.toList()
      ..sort((a, b) => a.compareTo(b));

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        ...sortedDates.map((date) {
          final tasksForDate = allTasksByDate[date]!;
          final convertedTasks = tasksForDate
              .map((task) => widget.convertScheduleToDatum(task))
              .toList();

          return Column(
            children: [
              // Date header
              Container(
                width: double.infinity,
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      DateFormat('EEE d MMM').format(date),
                      style: Theme.of(context)
                          .textTheme
                          .montserratTitleSmall
                          .copyWith(
                            color: isSameDay(date, DateTime.now())
                                ? AppColors.primaryBlue
                                : Colors.black,
                          ),
                    ),
                    Text(
                      widget.calculateTotalHours(tasksForDate),
                      style: Theme.of(context)
                          .textTheme
                          .montserratTitleExtraSmall
                          .copyWith(color: Colors.black),
                    ),
                  ],
                ),
              ),

              // Task list for this date
              ReorderableStoreList(
                tasks: convertedTasks,
                isCalendarMode: widget.isCheckboxMode,
                showScheduledDate: false,
                showTickIndicator: true,
                showAllDisclosureIndicator: false,
                permanentlyDisableAllDisclosureIndicator: false,
                isOpenTask: true,
                onSelectionChanged: widget.onSelectionChanged,
                selectAll: widget.areAllItemsSelected,
                onTaskTap: widget.onTaskTap,
                enableTimeValidation: true,
              ),

              const Divider(height: 1),
            ],
          );
        }),

        // Add bottom padding when in checkbox mode
        if (widget.isCheckboxMode) const SizedBox(height: 120),
      ],
    );
  }
}
