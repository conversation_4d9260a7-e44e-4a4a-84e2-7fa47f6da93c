import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/photo_upload_widget.dart'
    show PhotoUploadWidget;
import 'package:storetrack_app/shared/widgets/offline_image_widget.dart';

class RadioButtonWidget extends StatefulWidget {
  final Measurement measurement;
  final String? value;
  final Function(String?) onChanged;
  final Widget? conditionalWidget; // Optional conditional widget to show
  final bool showCameraIcon;
  final bool isCameraMandatory;
  final VoidCallback? onCameraTap;
  final bool isRequired;
  final List<String> selectedImages;
  final String? photoErrorText;
  final PhotoTagsT? photoTag;
  final String? description;
  // New properties for multiple photo tags support
  final List<PhotoTagsT>? photoTags;
  final Map<int, List<String>>? photosByTag;
  // Parameters for direct image capture
  final String? taskId;
  final String? formId;
  final String? questionId;
  final String? questionPartId;
  final VoidCallback? onPhotoAdded;

  const RadioButtonWidget({
    super.key,
    required this.measurement,
    required this.value,
    required this.onChanged,
    this.conditionalWidget,
    this.showCameraIcon = false,
    this.isCameraMandatory = false,
    this.onCameraTap,
    this.isRequired = false,
    this.selectedImages = const [],
    this.photoErrorText,
    this.photoTag,
    this.description,
    this.photoTags,
    this.photosByTag,
    this.taskId,
    this.formId,
    this.questionId,
    this.questionPartId,
    this.onPhotoAdded,
  });

  @override
  State<RadioButtonWidget> createState() => _RadioButtonWidgetState();
}

class _RadioButtonWidgetState extends State<RadioButtonWidget> {
  final TextEditingController _conditionalTextController =
      TextEditingController();
  String? _conditionalText;

  @override
  void dispose() {
    _conditionalTextController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final options = widget.measurement.measurementOptions ?? [];

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(vertical: 4.0),
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.black10,
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title with required indicator and thumbnail
          Row(
            children: [
              // Measurement thumbnail (if available)
              if (widget.description == null &&
                  widget.measurement.measurementImage != null &&
                  widget.measurement.measurementImage!.isNotEmpty &&
                  widget.measurement.measurementImage != "0")
                OfflineImageWidget(
                  url: widget.measurement.measurementImage,
                  width: 60.0,
                  height: 60.0,
                ),
              if (widget.description == null &&
                  widget.measurement.measurementImage != null &&
                  widget.measurement.measurementImage!.isNotEmpty &&
                  widget.measurement.measurementImage != "0")
                const Gap(12),
              Expanded(
                child: Text(
                  widget.description ??
                      widget.measurement.measurementDescription ??
                      'Select an option',
                  style: textTheme.montserratTitleExtraSmall,
                ),
              ),
              if (widget.isRequired)
                Container(
                  width: 16,
                  height: 16,
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.priority_high,
                    color: AppColors.loginRed,
                    size: 16,
                  ),
                ),
            ],
          ),
          const Gap(16),
          Row(
            children: options.map((option) {
              final optionId = option.measurementOptionId?.toString();
              final isSelected = widget.value == optionId;

              return Expanded(
                child: GestureDetector(
                  onTap: () {
                    widget.onChanged(optionId);
                    // Check if this triggers conditional text field
                    if (optionId != null &&
                        option.measurementOptionDescription?.toLowerCase() ==
                            'no') {
                      // Show conditional text field for "No" option
                      setState(() {
                        _conditionalText = '';
                      });
                    } else {
                      // Hide conditional text field for other options
                      setState(() {
                        _conditionalText = null;
                        _conditionalTextController.clear();
                      });
                    }
                  },
                  child: Container(
                    margin: const EdgeInsets.only(right: 12.0),
                    child: Row(
                      children: [
                        Container(
                          width: 20,
                          height: 20,
                          decoration: BoxDecoration(
                            color: isSelected
                                ? AppColors.primaryBlue
                                : Colors.white,
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: isSelected
                                  ? AppColors.primaryBlue
                                  : AppColors.blackTint2,
                              width: 2,
                            ),
                          ),
                          child: isSelected
                              ? Container(
                                  width: 8,
                                  height: 8,
                                  margin: const EdgeInsets.all(4),
                                  decoration: const BoxDecoration(
                                    color: Colors.white,
                                    shape: BoxShape.circle,
                                  ),
                                )
                              : null,
                        ),
                        const Gap(8),
                        Expanded(
                          child: Text(
                            option.measurementOptionDescription ?? 'Option',
                            style: textTheme.montserratTitleExtraSmall.copyWith(
                              color: AppColors.black,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
          // Conditional widget (existing)
          if (widget.conditionalWidget != null) ...[
            const Gap(16),
            widget.conditionalWidget!,
          ],
          // Conditional text field for "No" selection
          if (_conditionalText != null) ...[
            const Gap(16),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Please specify why',
                  style: textTheme.montserratTitleExtraSmall.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppColors.black,
                  ),
                ),
                const Gap(4),
                Text(
                  'Optional',
                  style: textTheme.montserratTableSmall.copyWith(
                    color: AppColors.blackTint1,
                    fontStyle: FontStyle.italic,
                  ),
                ),
                const Gap(12),
                TextFormField(
                  controller: _conditionalTextController,
                  onChanged: (value) {
                    setState(() {
                      _conditionalText = value;
                    });
                  },
                  maxLines: 3,
                  decoration: InputDecoration(
                    hintText: 'Enter your text here...',
                    hintStyle: textTheme.montserratTitleExtraSmall.copyWith(
                      color: AppColors.blackTint1,
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10.0),
                      borderSide: const BorderSide(color: AppColors.blackTint2),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10.0),
                      borderSide: const BorderSide(color: AppColors.blackTint2),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10.0),
                      borderSide: const BorderSide(
                          color: AppColors.primaryBlue, width: 2),
                    ),
                    filled: true,
                    fillColor: Colors.white,
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12.0,
                      vertical: 16.0,
                    ),
                  ),
                  style: textTheme.montserratTitleExtraSmall.copyWith(
                    color: AppColors.black,
                  ),
                ),
              ],
            ),
          ],
          // Camera section - support multiple PhotoUploadWidgets based on photoTags
          if (widget.showCameraIcon) ...[
            const Gap(16),
            // Use multiple photo tags if available, otherwise fall back to single photo tag
            if (widget.photoTags != null && widget.photoTags!.isNotEmpty)
              ..._buildPhotoUploadWidgets()
            else if (widget.photoTag != null)
              PhotoUploadWidget(
                selectedImages: widget.selectedImages,
                errorText: widget.photoErrorText,
                photoTag: widget.photoTag,
                // Use direct image capture if parameters are available
                onCameraPressed:
                    (widget.taskId != null && widget.questionId != null)
                        ? null
                        : () {
                            if (widget.onCameraTap != null) {
                              widget.onCameraTap!();
                            }
                          },
                onImagesTap: () {
                  if (widget.onCameraTap != null) {
                    widget.onCameraTap!();
                  }
                },
                // Parameters for direct image capture
                taskId: widget.taskId,
                formId: widget.formId,
                questionId: widget.questionId,
                questionPartId: widget.questionPartId,
                measurementId: widget.measurement.measurementId?.toString(),
                level: 3, // Level 3 for photo_tags_three
                onPhotoAdded: widget.onPhotoAdded,
              ),
          ],
          if (widget.measurement.required == true && widget.value == null)
            Padding(
              padding: const EdgeInsets.only(top: 12.0),
              child: Text(
                'This field is required',
                style: textTheme.montserratTableSmall.copyWith(
                  color: Colors.red,
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// Get photos for a specific photo tag ID
  List<String> _getPhotosForTag(int photoTagId) {
    return widget.photosByTag?[photoTagId] ?? [];
  }

  /// Build PhotoUploadWidget for each photo tag
  List<Widget> _buildPhotoUploadWidgets() {
    if (widget.photoTags == null || widget.photoTags!.isEmpty) {
      return [];
    }

    final List<Widget> photoWidgets = [];

    for (int i = 0; i < widget.photoTags!.length; i++) {
      final photoTag = widget.photoTags![i];
      final photoTagId = photoTag.photoTagId?.toInt() ?? 0;

      photoWidgets.add(
        PhotoUploadWidget(
          selectedImages: _getPhotosForTag(photoTagId),
          errorText: widget.photoErrorText,
          photoTag: photoTag,
          // Use direct image capture if parameters are available
          onCameraPressed: (widget.taskId != null && widget.questionId != null)
              ? null
              : () {
                  if (widget.onCameraTap != null) {
                    widget.onCameraTap!();
                  }
                },
          onImagesTap: () {
            if (widget.onCameraTap != null) {
              widget.onCameraTap!();
            }
          },
          // Parameters for direct image capture
          taskId: widget.taskId,
          formId: widget.formId,
          questionId: widget.questionId,
          questionPartId: widget.questionPartId,
          measurementId: widget.measurement.measurementId?.toString(),
          level: 3, // Level 3 for photo_tags_three
          onPhotoAdded: widget.onPhotoAdded,
        ),
      );

      // Add gap between photo upload widgets if there are multiple
      if (i < widget.photoTags!.length - 1) {
        photoWidgets.add(const Gap(16));
      }
    }

    return photoWidgets;
  }
}
