import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/config/themes/app_typography.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as entities;
import 'package:storetrack_app/features/home/<USER>/widgets/empty_state.dart';
import 'package:storetrack_app/shared/widgets/html_text_widget.dart';

class BriefContentWidget extends StatefulWidget {
  final entities.TaskDetail task;

  const BriefContentWidget({
    super.key,
    required this.task,
  });

  @override
  State<BriefContentWidget> createState() => _BriefContentWidgetState();
}

class _BriefContentWidgetState extends State<BriefContentWidget> {
  bool isOverviewExpanded = false;
  bool isBriefContentExpanded = false;

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return _buildBriefContent(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Overview Subsection
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Container(
            margin: const EdgeInsets.only(bottom: 12.0),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Column(
              children: [
                ListTile(
                  title: Text(
                    'Overview',
                    style: textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppColors.black,
                    ),
                  ),
                  trailing: Icon(
                    isOverviewExpanded
                        ? Icons.keyboard_arrow_up
                        : Icons.keyboard_arrow_right,
                    color: AppColors.black,
                    size: 20,
                  ),
                  onTap: () {
                    setState(() {
                      isOverviewExpanded = !isOverviewExpanded;
                    });
                  },
                ),
                if (isOverviewExpanded) ...[
                  Container(
                    padding: const EdgeInsets.all(16.0),
                    decoration: BoxDecoration(
                      border: Border(
                        top: BorderSide(color: Colors.grey.shade200),
                      ),
                    ),
                    child: _buildOverviewContent(context),
                  ),
                ],
              ],
            ),
          ),
        ),

        // Brief Subsection
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Container(
            margin: const EdgeInsets.only(bottom: 12.0),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Column(
              children: [
                ListTile(
                  title: Text(
                    'Brief',
                    style: textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppColors.black,
                    ),
                  ),
                  trailing: Icon(
                    isBriefContentExpanded
                        ? Icons.keyboard_arrow_up
                        : Icons.keyboard_arrow_right,
                    color: AppColors.black,
                    size: 20,
                  ),
                  onTap: () {
                    setState(() {
                      isBriefContentExpanded = !isBriefContentExpanded;
                    });
                  },
                ),
                if (isBriefContentExpanded) ...[
                  Container(
                    padding: const EdgeInsets.all(16.0),
                    decoration: BoxDecoration(
                      border: Border(
                        top: BorderSide(color: Colors.grey.shade200),
                      ),
                    ),
                    child: _buildBriefContent(context),
                  ),
                ],
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildOverviewContent(BuildContext context) {
    return const SizedBox(
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'The summary of the brief will be shown here',
            style: AppTypography.montserratParagraphSmall,
          ),
        ],
      ),
    );
  }

  Widget _buildBriefContent(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.task.forms != null && widget.task.forms!.isNotEmpty)
          ...widget.task.forms!.asMap().entries.map((entry) {
            final index = entry.key;
            final form = entry.value;

            return _buildPriorityFormItem(
                context, index + 1, form, widget.task);
          })
        else
          const EmptyState(message: 'No brief available'),
      ],
    );
  }

  Widget _buildPriorityFormItem(
    BuildContext context,
    int priorityNumber,
    entities.Form form,
    entities.TaskDetail task,
  ) {
    final textTheme = Theme.of(context).textTheme;
    final visibleQuestions = form.questions
        ?.where((q) => q.questionBrief?.isNotEmpty == true)
        .toList();
    return FittedBox(
      child: Row(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      border: Border.all(color: AppColors.black20),
                      shape: BoxShape.circle,
                    ),
                    child: Center(
                      child: Text(
                        priorityNumber.toString(),
                        style: textTheme.bodySmall?.copyWith(
                          color: AppColors.primaryBlue,
                          fontWeight: FontWeight.w600,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ),
                  const Gap(12),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        form.formName ?? 'Unnamed Form',
                        style: textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: AppColors.black,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              Row(
                children: [
                  if (!(form.questions == null || form.questions!.isEmpty))
                    Padding(
                      padding: const EdgeInsets.only(left: 10.0),
                      child: Container(
                        width: 2,
                        height: 60,
                        color: AppColors.black10,
                        margin: const EdgeInsets.only(top: 0),
                      ),
                    ),
                  priorityNumber == (task.forms?.length ?? 0)
                      ? const Gap(36)
                      : const Gap(26),
                  ...form.questions!.asMap().entries.map(
                    (entry) {
                      if (entry.value.questionBrief?.isNotEmpty == true) {
                        final index = entry.key;
                        final question = entry.value;
                        final isLastQuestion =
                            index == form.questions!.length - 1;
                        return Padding(
                          padding: EdgeInsets.only(
                              top: priorityNumber == (task.forms?.length ?? 0)
                                  ? 12.0
                                  : 0),
                          child: _buildPriorityQuestionItem(
                              context, form, question, isLastQuestion),
                        );
                      } else {
                        return Container();
                      }
                    },
                  ),
                  (visibleQuestions == null || visibleQuestions.isEmpty)
                      ? Padding(
                          padding: EdgeInsets.only(
                              top: priorityNumber == (task.forms?.length ?? 0)
                                  ? 12.0
                                  : 0),
                          child: Text(
                            'N/A',
                            style: textTheme.bodySmall?.copyWith(
                              fontWeight: FontWeight.w500,
                              color: AppColors.black,
                            ),
                          ),
                        )
                      : Container(),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPriorityQuestionItem(BuildContext context, entities.Form form,
      entities.Question question, bool isLastQuestion) {
    final textTheme = Theme.of(context).textTheme;

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: isLastQuestion
            ? const BorderRadius.only(
                bottomLeft: Radius.circular(10),
                bottomRight: Radius.circular(10),
              )
            : null,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (question.questionBrief?.isNotEmpty == true)
            Padding(
              padding: const EdgeInsets.all(2.0),
              child: SizedBox(
                width: MediaQuery.of(context).size.width - 100,
                child: HtmlTextWidget(
                  text: question.questionBrief ?? 'No question brief',
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  style: textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.w500,
                    color: AppColors.black,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
