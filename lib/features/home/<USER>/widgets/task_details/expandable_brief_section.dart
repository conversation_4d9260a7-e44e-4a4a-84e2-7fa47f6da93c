import 'package:flutter/material.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as entities;
import 'package:storetrack_app/features/home/<USER>/widgets/task_details/brief_content_widget.dart';

class ExpandableBriefSection extends StatefulWidget {
  final entities.TaskDetail task;

  const ExpandableBriefSection({
    super.key,
    required this.task,
  });

  @override
  State<ExpandableBriefSection> createState() => _ExpandableBriefSectionState();
}

class _ExpandableBriefSectionState extends State<ExpandableBriefSection> {
  bool isBriefExpanded = false;

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(0),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Brief Section Header
          Padding(
            padding: EdgeInsets.only(
              top: 20,
              left: 16,
              right: 16,
              bottom: isBriefExpanded ? 0 : 20,
            ),
            child: GestureDetector(
              onTap: () {
                setState(() {
                  isBriefExpanded = !isBriefExpanded;
                });
              },
              child: Container(
                color: Colors.transparent,
                width: double.infinity,
                padding: const EdgeInsets.symmetric(vertical: 0.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text('Brief', style: textTheme.montserratTitleSmall),
                    Icon(
                      isBriefExpanded
                          ? Icons.keyboard_arrow_up
                          : Icons.keyboard_arrow_right,
                      color: AppColors.black,
                      size: 20,
                    ),
                  ],
                ),
              ),
            ),
          ),

          // Brief Section Content
          if (isBriefExpanded) BriefContentWidget(task: widget.task),
        ],
      ),
    );
  }
}
