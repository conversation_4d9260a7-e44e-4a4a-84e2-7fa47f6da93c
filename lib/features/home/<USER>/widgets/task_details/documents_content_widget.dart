import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/constants/app_assets.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as entities;
import 'package:storetrack_app/features/home/<USER>/widgets/empty_state.dart';
import 'package:storetrack_app/core/services/docs_interaction_service.dart';
import 'package:storetrack_app/features/home/<USER>/entities/opened_doc_entity.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/shared/widgets/offline_document_widget.dart';

class DocumentsContentWidget extends StatefulWidget {
  final entities.TaskDetail task;

  const DocumentsContentWidget({
    super.key,
    required this.task,
  });

  @override
  State<DocumentsContentWidget> createState() => _DocumentsContentWidgetState();
}

class _DocumentsContentWidgetState extends State<DocumentsContentWidget> {
  Set<num?> expandedDocuments = {};

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: widget.task.documents == null || widget.task.documents!.isEmpty
          ? const EmptyState(
              message: 'No documents available',
              margin: EdgeInsets.symmetric(vertical: 8),
            )
          : Column(
              children: widget.task.documents!.map((document) {
                return _buildDocumentNameItem(context, document);
              }).toList(),
            ),
    );
  }

  Widget _buildDocumentNameItem(
      BuildContext context, entities.Document document) {
    final textTheme = Theme.of(context).textTheme;
    final isExpanded = expandedDocuments.contains(document.documentId);

    return Container(
      margin: const EdgeInsets.only(bottom: 12.0),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          ListTile(
            leading: Container(
              width: 40,
              height: 40,
              alignment: Alignment.centerLeft,
              child: document.documentIconLink != null
                  ? Image.network(
                      document.documentIconLink!,
                      width: 40,
                      height: 40,
                      fit: BoxFit.cover,
                      color: Colors.black,
                    )
                  : Image.asset(
                      AppAssets.taskReport,
                      width: 40,
                      height: 40,
                      scale: 3,
                      color: Colors.black,
                    ),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16.0, vertical: 2.0),
            title: Text(
              document.documentName ?? 'Unnamed Document',
              style: textTheme.montserratTitleExtraSmall.copyWith(
                color: Colors.black,
              ),
            ),
            trailing: Icon(
              isExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
              color: AppColors.black,
              size: 20,
            ),
            onTap: () {
              setState(() {
                if (isExpanded) {
                  expandedDocuments.remove(document.documentId);
                } else {
                  expandedDocuments.add(document.documentId);
                }
              });
            },
          ),
          if (isExpanded) ...[
            if (document.files == null || document.files!.isEmpty)
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  'No files available for this document',
                  style: textTheme.bodySmall?.copyWith(
                    color: AppColors.black.withValues(alpha: 0.6),
                  ),
                ),
              )
            else
              ...document.files!.map((file) {
                return _buildDocumentFileItem(context, file, document);
              }),
            const Gap(12),
          ],
        ],
      ),
    );
  }

  Widget _buildDocumentFileItem(BuildContext context, entities.FileElement file,
      entities.Document document) {
    final textTheme = Theme.of(context).textTheme;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 4.0),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: OfflineDocumentWidget(
        url: file.documentFileLink ?? '',
        title: "Document",
        onTap: () => _saveDocumentInteraction(file.fileId),
        child: ListTile(
          dense: true,
          leading: Icon(
            _getFileIcon(file.documentFileLink),
            color: AppColors.black,
            size: 20,
          ),
          title: Text(
            (file.documentFileLink ?? 'Unnamed File').trim().split('\n').last,
            style: textTheme.bodySmall?.copyWith(
              color: Colors.black,
              fontWeight: FontWeight.w500,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ),
    );
  }

  IconData _getFileIcon(String? fileUrl) {
    if (fileUrl == null) return Icons.description;
    final url = fileUrl.toLowerCase();
    if (url.contains('.pdf')) {
      return Icons.picture_as_pdf;
    } else if (url.contains('.jpg') ||
        url.contains('.jpeg') ||
        url.contains('.png') ||
        url.contains('.gif')) {
      return Icons.image;
    } else if (url.contains('youtube.com') || url.contains('youtu.be')) {
      return Icons.play_circle;
    } else if (url.contains('.txt') ||
        url.contains('.doc') ||
        url.contains('.docx')) {
      return Icons.description;
    } else {
      return Icons.insert_drive_file;
    }
  }

  // Function to save document interaction
  Future<void> _saveDocumentInteraction(num? fileId) async {
    if (fileId == null) {
      logger('Cannot save document interaction: fileId is null');
      return;
    }

    try {
      final docsService = sl<DocsInteractionService>();
      final openedDoc = OpenedDocItemEntity(
        docId: fileId.toString(),
        timeStamp: DateTime.now().toIso8601String(),
      );

      final success = await docsService.saveOpenedDoc(openedDoc);
      if (!success) {
        logger('Failed to save document interaction for fileId: $fileId');
      }
    } catch (e) {
      logger('Error saving document interaction: $e');
    }
  }
}
