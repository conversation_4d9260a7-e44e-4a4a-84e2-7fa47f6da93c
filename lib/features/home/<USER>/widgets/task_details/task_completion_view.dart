import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/core/utils/form_utils.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as entities;
import 'package:storetrack_app/features/home/<USER>/widgets/segment_indicator.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/form_refresh/form_refresh_cubit.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/form_refresh/form_refresh_state.dart';
import 'package:storetrack_app/core/services/timer_service.dart';
import 'package:get_it/get_it.dart';

class TaskCompletionView extends StatefulWidget {
  final entities.TaskDetail task;
  final FormProgress? formProgress;

  const TaskCompletionView({
    super.key,
    required this.task,
    this.formProgress,
  });

  @override
  State<TaskCompletionView> createState() => _TaskCompletionViewState();
}

class _TaskCompletionViewState extends State<TaskCompletionView> {
  FormProgress? _currentFormProgress;
  bool _isLoading = false;
  int? _actualMinutes; // Add this to store actual minutes from timer

  @override
  void initState() {
    super.initState();
    _currentFormProgress = widget.formProgress;
    // Calculate initial progress if not provided
    if (_currentFormProgress == null && widget.task.taskId != null) {
      _calculateFormProgress();
    }
    _fetchActualMinutes(); // Fetch timer minutes on init
  }

  @override
  void didUpdateWidget(TaskCompletionView oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Update current progress if the widget's formProgress changed
    if (oldWidget.formProgress != widget.formProgress) {
      _currentFormProgress = widget.formProgress;
    }
    // Recalculate if task changed
    if (oldWidget.task.taskId != widget.task.taskId) {
      _calculateFormProgress();
      _fetchActualMinutes(); // Refetch timer minutes if task changes
    }
  }

  Future<void> _calculateFormProgress() async {
    if (!mounted || widget.task.taskId == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final progress = await FormUtils.getFormPageProgress(
        taskId: widget.task.taskId!.toInt(),
      );
      if (mounted) {
        setState(() {
          _currentFormProgress = progress;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _fetchActualMinutes() async {
    // Use TimerService to get elapsed time for this task
    final timerService = GetIt.instance<TimerService>();
    final taskId = widget.task.taskId;
    if (taskId != null) {
      final task = await timerService.getTaskById(taskId.toInt());
      if (task != null) {
        final elapsed = timerService.calculateElapsedTime(task);
        if (mounted) {
          setState(() {
            _actualMinutes = elapsed.inMinutes;
          });
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return BlocListener<FormRefreshCubit, FormRefreshState>(
      listener: (context, state) {
        if (state is RefreshForm) {
          // Recalculate form progress when refresh is triggered
          _calculateFormProgress();
          _fetchActualMinutes(); // Also refresh timer minutes
        }
      },
      child: _buildContent(textTheme),
    );
  }

  Widget _buildContent(TextTheme textTheme) {
    // Use current form progress if available, otherwise fallback to existing logic
    final max =
        _currentFormProgress?.totalVisible ?? widget.task.forms?.length ?? 0;
    final completed = _currentFormProgress?.totalCompleted ??
        widget.task.ctFormsCompletedCnt ??
        0;

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 22),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text('Task Completion', style: textTheme.montserratTitleXxsmall),
          const Gap(8),
          Row(
            children: [
              if (_isLoading)
                const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    color: AppColors.primaryBlue,
                  ),
                )
              else
                Text(
                  (completed == 0 && max == 0)
                      ? '0%'
                      : '${((completed / max) * 100).toStringAsFixed(0)}%',
                  style: textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
            ],
          ),
          const Gap(8),
          LayoutBuilder(
            builder: (context, constraints) {
              final progressWidth = constraints.maxWidth;
              final progress =
                  (completed == 0 && max == 0) ? 0.0 : (completed / max);

              // Calculate position based on the width of the SegmentedProgressIndicator
              final position = progressWidth * progress;

              // Center the line (line width is 1.5)
              final adjustedPosition = position - 0.75;

              return Stack(
                clipBehavior: Clip.none,
                children: [
                  SegmentedProgressIndicator(
                    progress: progress,
                    totalWidth: progressWidth,
                    activeColor: AppColors.primaryBlue,
                    backgroundColor: Colors.grey.shade200,
                    dividerColor: Colors.black,
                    height: 10,
                    segments: 10,
                    borderRadius: 10,
                  ),
                  Positioned(
                    left: adjustedPosition,
                    top: -8,
                    bottom: 0,
                    child: Container(
                      width: 1.5,
                      color: Colors.black,
                    ),
                  ),
                ],
              );
            },
          ),
          const Gap(8),
          Text('$completed of $max forms',
              style: textTheme.montserratTableSmall),
          if (_actualMinutes != null && _actualMinutes! > 0) ...[
            const Gap(8),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.timer, size: 16, color: AppColors.primaryBlue),
                const Gap(6),
                Text('Actual minutes: $_actualMinutes',
                    style: textTheme.montserratTableSmall.copyWith(
                      color: AppColors.primaryBlue,
                      fontWeight: FontWeight.w600,
                    )),
              ],
            ),
          ],
        ],
      ),
    );
  }
}
