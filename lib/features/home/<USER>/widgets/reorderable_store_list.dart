import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:storetrack_app/core/constants/app_assets.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import '../../../../core/utils/snackbar_service.dart';
import '../../../../core/utils/task_grouping.dart';
import '../../../../core/storage/data_manager.dart';
import '../../../../di/service_locator.dart';
import '../constants/action_types.dart';
import '../widgets/task_action_button.dart';
import 'store_card.dart';
import 'empty_state.dart';
import 'package:auto_route/auto_route.dart';
import '../../../../config/routes/app_router.gr.dart';

class ReorderableStoreList extends StatefulWidget {
  final List<TaskDetail> tasks;
  final bool isCalendarMode;
  final bool showScheduledDate;
  final bool showTickIndicator;
  final bool showAllDisclosureIndicator;
  final bool permanentlyDisableAllDisclosureIndicator;
  final bool isOpenTask;
  final Function(List<TaskDetail> allItems, List<TaskDetail> selectedItems)?
      onSelectionChanged;
  final bool selectAll;
  final void Function(TaskDetail task)? onTaskTap;
  final bool enableTimeValidation;
  final bool disableTaskNavigation;

  const ReorderableStoreList({
    super.key,
    required this.tasks,
    required this.isCalendarMode,
    this.showScheduledDate = false,
    this.showTickIndicator = false,
    this.showAllDisclosureIndicator = false,
    this.permanentlyDisableAllDisclosureIndicator = false,
    this.isOpenTask = false,
    this.onSelectionChanged,
    this.selectAll = false,
    this.onTaskTap,
    this.enableTimeValidation = false,
    this.disableTaskNavigation = false,
  });

  @override
  State<ReorderableStoreList> createState() => _ReorderableStoreListState();
}

class _ReorderableStoreListState extends State<ReorderableStoreList> {
  late List<TaskDetail> _tasks;
  late List<List<TaskDetail>> _tasksNew;
  late List<Widget> _parentActions;
  final Set<String> _selectedTaskIds = {}; // Track selected task IDs
  final Map<String, bool> _checkboxStates =
      {}; // Track checkbox states for all tasks
  late final DataManager _dataManager;

  @override
  void initState() {
    super.initState();
    _dataManager = sl<DataManager>();
    _tasks = List<TaskDetail>.from(widget.tasks);
    // Initialize with default grouping first to avoid LateInitializationError
    _tasksNew = groupTasksByStore(_tasks);
    _buildActionButtons();
    _initializeCheckboxStates();

    // Initialize with selectAll state if needed
    if (widget.selectAll) {
      _doSelectAll(true);
    }

    // Load saved order asynchronously after initial setup
    _loadSavedTaskOrder();
  }

  // Load saved task order asynchronously
  void _loadSavedTaskOrder() async {
    try {
      final savedOrder = await _dataManager.getTaskOrder();
      if (savedOrder != null && savedOrder.isNotEmpty) {
        setState(() {
          _applySavedTaskOrder(savedOrder);
        });
      }
      // If no saved order exists, keep the default grouping already set
    } catch (e) {
      // If there's an error loading saved order, keep the default grouping
      debugPrint('Error loading saved task order: $e');
    }
  }

  // Apply saved task order to the current tasks
  void _applySavedTaskOrder(List<String> savedOrder) {
    // Create a map for quick lookup of tasks by ID
    Map<String, TaskDetail> taskMap = {};
    for (var task in _tasks) {
      taskMap[task.taskId.toString()] = task;
    }

    // Create ordered list based on saved order
    List<TaskDetail> orderedTasks = [];

    // First, add tasks in the saved order
    for (String taskId in savedOrder) {
      if (taskMap.containsKey(taskId)) {
        orderedTasks.add(taskMap[taskId]!);
        taskMap.remove(taskId); // Remove to avoid duplicates
      }
    }

    // Then add any new tasks that weren't in the saved order
    orderedTasks.addAll(taskMap.values);

    // Update the tasks list and regroup
    _tasks = orderedTasks;
    _tasksNew = groupTasksByStore(_tasks);
  }

  // Extract all task IDs in their current order
  List<String> _extractCurrentTaskOrder() {
    return _tasks.map((task) => task.taskId.toString()).toList();
  }

  // Save current task order
  Future<void> _saveCurrentTaskOrder() async {
    try {
      final currentOrder = _extractCurrentTaskOrder();
      await _dataManager.saveTaskOrder(currentOrder);
    } catch (e) {
      // Handle error silently to avoid disrupting user experience
      debugPrint('Error saving task order: $e');
    }
  }

  // Initialize checkbox states for all tasks
  void _initializeCheckboxStates() {
    _checkboxStates.clear();
    for (var taskGroup in _tasksNew) {
      for (var task in taskGroup) {
        _checkboxStates[task.taskId.toString()] = false;
      }
    }
  }

  // Helper method to get all selected tasks
  List<TaskDetail> _getSelectedTasks() {
    return _tasks
        .where((task) => _selectedTaskIds.contains(task.taskId.toString()))
        .toList();
  }

  // Handle selection change and notify parent
  void _handleSelectionChanged() {
    if (widget.onSelectionChanged != null) {
      // Direct callback for user interactions
      widget.onSelectionChanged!(_tasks, _getSelectedTasks());
    }
  }

  // Select or deselect all tasks - this can be called from props (didUpdateWidget) or user action
  void selectAll(bool selectAll) {
    // For safety, always use post-frame callback when called from didUpdateWidget
    // This ensures we don't call setState during build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _doSelectAll(selectAll);
    });
  }

  // Helper method to perform the actual selection update
  void _doSelectAll(bool selectAll) {
    // We need to update the checkbox states directly without setState here
    // since this might be called during build
    if (selectAll) {
      // Select all tasks
      _selectedTaskIds.clear();
      for (var taskGroup in _tasksNew) {
        for (var task in taskGroup) {
          _selectedTaskIds.add(task.taskId.toString());
          _checkboxStates[task.taskId.toString()] = true;
        }
      }
    } else {
      // Deselect all tasks
      _selectedTaskIds.clear();
      for (var key in _checkboxStates.keys) {
        _checkboxStates[key] = false;
      }
    }

    // We don't notify the parent here since this is coming from the parent
    // and would create an infinite loop.
    // The parent already knows the selection state.
  }

  // Handle parent checkbox change
  void _handleParentCheckboxChanged(bool? value, TaskDetail parentTask) {
    final bool isChecked = value ?? false;
    HapticFeedback.selectionClick();
    setState(() {
      // Get all subtasks for this parent
      final List<TaskDetail> subtasks = _tasksNew.firstWhere(
        (group) => group.isNotEmpty && group.first.taskId == parentTask.taskId,
        orElse: () => [],
      );

      // Update checkbox state for parent
      _checkboxStates[parentTask.taskId.toString()] = isChecked;

      // Update selection and checkbox state for all subtasks
      for (var task in subtasks) {
        final taskId = task.taskId.toString();
        // Update selection tracking
        if (isChecked) {
          _selectedTaskIds.add(taskId);
        } else {
          _selectedTaskIds.remove(taskId);
        }
        // Update checkbox state
        _checkboxStates[taskId] = isChecked;
      }
    });

    // Notify parent of selection change
    _handleSelectionChanged();
  }

  // Handle subtask checkbox change
  void _handleSubtaskCheckboxChanged(bool? value, TaskDetail subtask) {
    final bool isChecked = value ?? false;
    final String taskId = subtask.taskId.toString();

    HapticFeedback.selectionClick();
    setState(() {
      // Update selection tracking
      if (isChecked) {
        _selectedTaskIds.add(taskId);
      } else {
        _selectedTaskIds.remove(taskId);
      }

      // Update checkbox state
      _checkboxStates[taskId] = isChecked;

      // Find parent task group
      for (var taskGroup in _tasksNew) {
        if (taskGroup.any((task) => task.taskId == subtask.taskId)) {
          // Check if all subtasks are selected to update parent checkbox
          bool allSelected = taskGroup
              .every((task) => _checkboxStates[task.taskId.toString()] == true);

          // Update parent checkbox state if there's at least one task in the group
          if (taskGroup.isNotEmpty) {
            _checkboxStates[taskGroup.first.taskId.toString()] = allSelected;
          }

          break;
        }
      }
    });

    // Notify parent of selection change
    _handleSelectionChanged();
  }

  // --- Action Handlers (moved from unscheduled_page.dart) ---
  void _handleParentActionTap(String actionType, TaskDetail task) async {
    HapticFeedback.lightImpact();
    switch (actionType) {
      case ActionTypes.contactInfo:
        context.router.push(StoreInfoRoute(
          storeId: task.storeId?.toString() ?? '0',
          taskId: task.taskId?.toString() ?? '0',
        ));
        break;
      case ActionTypes.map:
        await _openGoogleMaps(task);
        break;
      case ActionTypes.chatAssistant:
        context.router.push(const AssistantRoute());
        break;
      default:
      // No action needed
    }
  }

  Future<void> _openGoogleMaps(TaskDetail task) async {
    try {
      double? latitude;
      double? longitude;
      if (task.taskLatitude != null && task.taskLongitude != null) {
        latitude = task.taskLatitude!.toDouble();
        longitude = task.taskLongitude!.toDouble();
      } else if (task.latitude != null && task.longitude != null) {
        latitude = task.latitude!.toDouble();
        longitude = task.longitude!.toDouble();
      }
      if (latitude == null || longitude == null) {
        SnackBarService.warning(
          context: context,
          message: 'Location coordinates not available for this task.',
        );
        return;
      }
      final googleMapsUrl =
          'https://www.google.com/maps?q=$latitude,$longitude';
      context.router
          .push(WebBrowserRoute(url: googleMapsUrl, title: 'Google Maps'));
    } catch (e) {
      SnackBarService.error(
        context: context,
        message: 'Error opening map: \\${e.toString()}',
      );
    }
  }

  Future<void> _handleChangeHelper(TaskDetail task) async {
    try {
      final userId = await _dataManager.getUserId();
      final scheduleId = task.scheduleId?.toString();

      if (!mounted) return;

      if (userId == null || userId.isEmpty) {
        SnackBarService.error(
          context: context,
          message: 'User ID not available. Please log in again.',
        );
        return;
      }

      if (scheduleId == null || scheduleId.isEmpty) {
        SnackBarService.error(
          context: context,
          message: 'Schedule ID not available for this task.',
        );
        return;
      }

      final helperUrl =
          'https://appservice.storetrack.com.au/standalone/Helpers.aspx?&pid=$userId&sid=$scheduleId';

      context.router
          .push(WebBrowserRoute(url: helperUrl, title: 'Change Helper'));
    } catch (e) {
      if (!mounted) return;
      SnackBarService.error(
        context: context,
        message: 'Error opening change helper: ${e.toString()}',
      );
    }
  }

  void _handleSubtaskActionTap(String actionType, TaskDetail task) async {
    HapticFeedback.lightImpact();
    switch (actionType) {
      case ActionTypes.changeHelper:
        await _handleChangeHelper(task);
        break;
      case ActionTypes.viewDocument:
        context.router.push(TaskFilesRoute(
          storeId: task.storeId ?? 0,
          taskId: task.taskId!.toInt(),
          sectionType: 'documents',
        ));
        break;
      case ActionTypes.viewPos:
        context.router.push(PosRoute(task: task));
        break;
      case ActionTypes.viewNote:
        context.navigateTo(NotesRoute(task: task));
        break;
      case ActionTypes.viewBrief:
        context.router.push(TaskFilesRoute(
          storeId: task.storeId ?? 0,
          taskId: task.taskId!.toInt(),
          sectionType: 'brief',
        ));
        break;
      case ActionTypes.chatAssistant:
        context.router.push(const AssistantRoute());
        break;
      default:
      // No action needed
    }
  }

  void _buildActionButtons() {
    if (widget.isCalendarMode) {
      _parentActions = [
        StatefulBuilder(
          builder: (context, setState) {
            return Checkbox(
              value: false, // This will be overridden in TaskCardNew
              onChanged: (value) {}, // This will be overridden in TaskCardNew
            );
          },
        ),
      ];
    } else {
      _parentActions = [
        TaskActionButton(
          icon: AppAssets.taskStore,
          actionType: ActionTypes.contactInfo,
          onPressed: () {
            var parentTask = _tasksNew.isNotEmpty ? _tasksNew[0][0] : null;
            if (parentTask != null) {
              _handleParentActionTap(ActionTypes.contactInfo, parentTask);
            }
          },
        ),
        TaskActionButton(
          icon: AppAssets.appbarMap,
          actionType: ActionTypes.map,
          onPressed: () {
            var parentTask = _tasksNew.isNotEmpty ? _tasksNew[0][0] : null;
            if (parentTask != null) {
              _handleParentActionTap(ActionTypes.map, parentTask);
            }
          },
        ),
        // TaskActionButton(
        //   icon: AppAssets.taskAssistant,
        //   actionType: ActionTypes.chatAssistant,
        //   onPressed: () {
        //     var parentTask = _tasksNew.isNotEmpty ? _tasksNew[0][0] : null;
        //     if (parentTask != null) {
        //       _handleParentActionTap(ActionTypes.chatAssistant, parentTask);
        //     }
        //   },
        // ),
      ];
    }
  }

  @override
  void didUpdateWidget(covariant ReorderableStoreList oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.tasks != widget.tasks ||
        oldWidget.isCalendarMode != widget.isCalendarMode) {
      _tasks = List<TaskDetail>.from(widget.tasks);
      // Initialize with default grouping first
      _tasksNew = groupTasksByStore(_tasks);
      _buildActionButtons();

      // Preserve checkbox states when tasks are updated
      // Only initialize new tasks, don't reset existing ones
      for (var taskGroup in _tasksNew) {
        for (var task in taskGroup) {
          final taskId = task.taskId.toString();
          if (!_checkboxStates.containsKey(taskId)) {
            _checkboxStates[taskId] = false;
          }
        }
      }

      // Load saved order asynchronously after updating tasks
      _loadSavedTaskOrder();
    }

    // Apply select all state if it changed - handle it directly
    if (oldWidget.selectAll != widget.selectAll) {
      _doSelectAll(widget.selectAll);
    }
  }

  void _handleSubtaskReorder(int taskIndex, int oldIndex, int newIndex) {
    setState(() {
      if (taskIndex < 0 || taskIndex >= _tasksNew.length) return;

      final taskGroup = _tasksNew[taskIndex];
      if (oldIndex < newIndex) {
        newIndex -= 1;
      }
      final task = taskGroup.removeAt(oldIndex);
      taskGroup.insert(newIndex, task);

      // Update the main tasks list to reflect the changes
      _tasks = _tasksNew.expand((group) => group).toList();
    });

    // Provide haptic feedback for successful reorder
    HapticFeedback.mediumImpact();

    // Save the new task order after reordering
    _saveCurrentTaskOrder();
  }

  void _handleTaskReorder(int oldIndex, int newIndex) {
    setState(() {
      if (oldIndex < newIndex) newIndex--;
      final taskGroup = _tasksNew.removeAt(oldIndex);
      _tasksNew.insert(newIndex, taskGroup);

      // Update the main tasks list to reflect the changes
      _tasks = _tasksNew.expand((group) => group).toList();
    });

    // Provide haptic feedback for successful reorder
    HapticFeedback.mediumImpact();

    // Save the new task order after reordering
    _saveCurrentTaskOrder();
  }

  // Helper to build subtask actions for a given subtask
  List<Widget> _buildSubtaskActionsFor(TaskDetail subtask) {
    final permanentlyDisable = widget.permanentlyDisableAllDisclosureIndicator;
    List<Widget> actions = [];

    // Change Helper
    if ((subtask.taskCount ?? 0) > 1 && subtask.teamlead == 1) {
      actions.add(TaskActionButton(
        icon: AppAssets.homeProfile,
        actionType: ActionTypes.changeHelper,
        onPressed: () =>
            _handleSubtaskActionTap(ActionTypes.changeHelper, subtask),
      ));
    }

    // View Document
    if (!permanentlyDisable) {
      actions.add(TaskActionButton(
        icon: AppAssets.taskReport,
        actionType: ActionTypes.viewDocument,
        onPressed: () =>
            _handleSubtaskActionTap(ActionTypes.viewDocument, subtask),
      ));
    }

    // View POS
    if (subtask.posRequired == true && !permanentlyDisable) {
      actions.add(TaskActionButton(
        icon: AppAssets.posIcon,
        actionType: ActionTypes.viewPos,
        onPressed: () => _handleSubtaskActionTap(ActionTypes.viewPos, subtask),
      ));
    }

    // View Note
    if ((subtask.taskNote != null && subtask.taskNote!.isNotEmpty) ||
        (subtask.comment != null && subtask.comment!.isNotEmpty)) {
      actions.add(TaskActionButton(
        icon: Icons.edit_outlined,
        actionType: ActionTypes.viewNote,
        onPressed: () => _handleSubtaskActionTap(ActionTypes.viewNote, subtask),
      ));
    }

    // View Brief
    bool hasBrief = subtask.forms != null &&
        subtask.forms!.isNotEmpty &&
        subtask.forms!.any((form) =>
            form.questions != null &&
            form.questions!.isNotEmpty &&
            form.questions!.any((question) =>
                question.questionBrief != null &&
                question.questionBrief!.isNotEmpty));
    if (hasBrief) {
      actions.add(TaskActionButton(
        icon: Icons.work_outline,
        actionType: ActionTypes.viewBrief,
        onPressed: () =>
            _handleSubtaskActionTap(ActionTypes.viewBrief, subtask),
      ));
    }

    // Chat Assistant
    // if (!permanentlyDisable) {
    //   actions.add(TaskActionButton(
    //     icon: AppAssets.taskAssistant,
    //     actionType: ActionTypes.chatAssistant,
    //     onPressed: () =>
    //         _handleSubtaskActionTap(ActionTypes.chatAssistant, subtask),
    //   ));
    // }

    return actions;
  }

  @override
  Widget build(BuildContext context) {
    // If there are no tasks, return an empty container
    if (_tasksNew.isEmpty) {
      return const EmptyState(message: 'No unscheduled tasks available');
    }

    int taskIndex = 0;
    List<Widget> listItems = [];
    for (var task1 in _tasksNew) {
      // Skip empty task groups
      if (task1.isEmpty) continue;

      var task = task1.first;
      final String keyValue =
          'task_${task.taskId}_${task.location ?? "unknown"}';

      // Build subtask actions for each subtask (assuming all subtasks in a group are similar)
      // If you want to show different actions per subtask, you can pass this list per subtask to StoreCard
      final subTaskActions = _buildSubtaskActionsFor(task1.first);

      listItems.add(
        ReorderableDelayedDragStartListener(
          index: taskIndex,
          key: ValueKey('drag_$keyValue'),
          enabled: true,
          child: StoreCard(
            heading: 'Priority tasks',
            key: ValueKey(keyValue),
            task: task1,
            parentActions: _parentActions,
            subTaskActions: subTaskActions,
            isCalendarMode: widget.isCalendarMode,
            showScheduledDate: widget.showScheduledDate,
            showTickIndicator: widget.showTickIndicator,
            showAllDisclosureIndicator: widget.showAllDisclosureIndicator,
            permanentlyDisableAllDisclosureIndicator:
                widget.permanentlyDisableAllDisclosureIndicator,
            isOpenTask: widget.isOpenTask,
            // Pass checkbox states
            initialParentCheckboxValue:
                _checkboxStates[task.taskId.toString()] ?? false,
            initialChildCheckboxStates: Map.fromEntries(task1.map((subtask) =>
                MapEntry(subtask.taskId.toString(),
                    _checkboxStates[subtask.taskId.toString()] ?? false))),
            onSubtaskReorder: (oldIndex, newIndex) {
              _handleSubtaskReorder(taskIndex, oldIndex, newIndex);
            },
            onParentSelectionChanged: (isSelected, parentTask) {
              _handleParentCheckboxChanged(isSelected, parentTask);
            },
            onSubtaskSelectionChanged: (isSelected, subtask) {
              _handleSubtaskCheckboxChanged(isSelected, subtask);
            },
            onSubtaskActionTap: _handleSubtaskActionTap,
            onTaskTap: widget.onTaskTap,
            enableTimeValidation: widget.enableTimeValidation,
            disableTaskNavigation: widget.disableTaskNavigation,
          ),
        ),
      );
      taskIndex++;
    }

    // If we have no valid items, show a message
    if (listItems.isEmpty) {
      return const EmptyState(message: 'No valid tasks to display');
    }

    // Wrap in LayoutBuilder to ensure proper constraints
    return LayoutBuilder(
      builder: (context, constraints) {
        return ReorderableListView(
          physics:
              const ClampingScrollPhysics(), // Keep original scroll physics
          buildDefaultDragHandles: false,
          shrinkWrap: true, // Ensure it takes only the space it needs
          padding:
              const EdgeInsets.symmetric(vertical: 8.0), // Add vertical padding
          onReorderStart: (index) {
            HapticFeedback.mediumImpact();
          },
          onReorder: _handleTaskReorder,
          proxyDecorator: (child, index, animation) {
            // Add a spring animation to the dragged item
            final curvedAnimation = CurvedAnimation(
              parent: animation,
              curve: Curves.easeInOut,
              reverseCurve: Curves.easeInOut,
            );

            return AnimatedBuilder(
              animation: curvedAnimation,
              builder: (context, child) {
                final scale =
                    1.0 + (curvedAnimation.value * 0.02); // Subtle scale effect

                return Material(
                  elevation: 4 * curvedAnimation.value,
                  color: Colors.transparent,
                  shadowColor: Colors.black.withValues(alpha: 0.15),
                  borderRadius: BorderRadius.circular(12),
                  child: Transform.scale(
                    scale: scale,
                    child: child,
                  ),
                );
              },
              child: child,
            );
          },
          children: listItems,
        );
      },
    );
  }
}
