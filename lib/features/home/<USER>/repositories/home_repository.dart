import 'package:storetrack_app/features/home/<USER>/models/availability_response.dart';
import 'package:storetrack_app/features/home/<USER>/models/history_response.dart';
import 'package:storetrack_app/features/home/<USER>/models/induction_response.dart';
import 'package:storetrack_app/features/home/<USER>/models/leave_response.dart';
import 'package:storetrack_app/features/home/<USER>/models/skills_response.dart';
import 'package:storetrack_app/features/home/<USER>/models/useful_links_response.dart';
import 'package:storetrack_app/features/home/<USER>/models/store_contact_model.dart';
import 'package:storetrack_app/features/home/<USER>/models/store_comment_model.dart';
import '../../domain/entities/profile_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/models/update_profile_request.dart';
import 'package:storetrack_app/features/home/<USER>/models/notification_response.dart';
import 'package:storetrack_app/features/home/<USER>/models/notification_req.dart';
import '../../domain/entities/checkin_request_entity.dart';
import '../../domain/entities/checkin_response_entity.dart';

import '../../../../shared/models/result.dart';
import '../../domain/entities/tasks_request_entity.dart';
import '../../domain/entities/tasks_response_entity.dart';
import '../../domain/entities/refresh_forms_entity.dart';
import '../../domain/entities/calendar_response_entity.dart';
import '../../domain/usecases/get_calendar_usecase.dart';
import '../../domain/entities/submit_report_request_entity.dart';
import '../../domain/entities/submit_report_response_entity.dart';
import '../../domain/entities/previous_tasks_response_entity.dart';
import '../../domain/entities/vacancy_entity.dart';
import '../../domain/entities/pos_request_entity.dart';
import '../../domain/entities/pos_response_entity.dart';
import '../../domain/entities/upload_photo_request_entity.dart';
import '../../domain/entities/upload_photo_response_entity.dart';
import '../../domain/entities/upload_sign_request_entity.dart';
import '../../domain/entities/upload_signature_response_entity.dart';
import '../../domain/entities/sync_pic_request_entity.dart';
import '../../domain/entities/sync_pic_response_entity.dart';
import '../../domain/entities/sync_sign_request_entity.dart';
import '../../domain/entities/sync_signature_response_entity.dart';
import '../../domain/entities/misc_setting_response_entity.dart';
import '../../domain/entities/emulate_user_request_entity.dart';
import '../../domain/entities/emulate_user_response_entity.dart';
import '../../domain/entities/create_task_request_entity.dart';
import '../../domain/entities/create_task_response_entity.dart';
import '../../domain/entities/availability_request_entity.dart';
import '../../domain/entities/user_state_request_entity.dart';
import '../../domain/entities/user_state_response_entity.dart';
import '../../domain/entities/opened_doc_entity.dart';
import '../models/coverage_store_model.dart';
import '../models/coverage_client_model.dart';

abstract class HomeRepository {
  Future<Result<TasksResponseEntity>> getTasks(
    TasksRequestEntity request, {
    bool isSync = false,
  });
  Future<Result<TasksResponseEntity>> refreshTasks(RefreshFormsEntity request);
  Future<Result<TaskDetail>> getTaskDetail(int taskId);
  Future<Result<CalendarResponseEntity>> getCalendarData(
    GetCalendarParams request, {
    bool isSync = false,
  });
  Future<Result<SubmitReportResponseEntity>> submitReport(
      SubmitReportRequestEntity request);

  // New submit report batch method
  Future<Result<SubmitReportResponseEntity>> submitReportBatch(
      SubmitReportRequestEntity request);

  // Create tasks method
  Future<Result<CreateTaskResponseEntity>> createTasks(
      CreateTaskRequestEntity request);

  Future<Result<PreviousTasksResponseEntity>> getPreviousTasks(
      PreviousTasksRequestEntity request);
  Future<Result<PreviousTasksResponseEntity>> getPreviousTasksOptimize(
      PreviousTasksRequestEntity request);
  Future<Result<UsefulLinksResponse>> getUsefulLinks({
    required String token,
    required String userId,
  });
  Future<Result<SkillsResponse>> getSkills({
    required String token,
    required String userId,
    bool isSync = false,
  });
  Future<Result<AvailabilityResponse>> getAvailability({
    required String token,
    required String userId,
    bool isSync = false,
  });
  Future<Result<InductionResponse>> getInduction({
    required String token,
    required String userId,
    bool isSync = false,
  });
  Future<Result<HistoryResponse>> getHistory({
    required String token,
    required String userId,
    bool isSync = false,
  });
  Future<Result<LeaveResponse>> getLeave({
    required String token,
    required String userId,
    bool isSync = false,
  });
  Future<Result<bool>> updateInduction({
    required String token,
    required String userId,
    required List<Map<String, dynamic>> inductions,
  });
  Future<Result<bool>> updateSkills({
    required String token,
    required String userId,
    required List<Map<String, dynamic>> skills,
  });
  Future<Result<bool>> updateAvailability(AvailabilityRequestEntity request);
  Future<Result<bool>> deleteLeave({
    required String token,
    required String userId,
    required String leaveId,
  });
  Future<Result<bool>> addLeave({
    required String token,
    required String userId,
    required DateTime startDate,
    required DateTime endDate,
  });

  // Store Contact methods
  Future<Result<StoreContactResponse>> getStoreContacts({
    required String token,
    required String userId,
    required String storeId,
  });

  Future<Result<bool>> updateStoreContact({
    required StoreContactRequest request,
  });

  Future<Result<StoreContactTypesResponse>> getStoreContactTypes({
    required String token,
    required String userId,
    required String storeId,
  });

  // Store Comment methods
  Future<Result<StoreCommentsResponse>> getStoreComments({
    required String taskId,
    required String userId,
    required String token,
  });

  Future<Result<bool>> saveStoreComment({
    required StoreCommentRequest request,
  });

  // Auto Schedule methods
  Future<Result<String>> getAutoScheduleLink({
    required String token,
    required String userId,
    int dayOffset = 0,
  });

  // Checkin methods
  Future<Result<CheckinResponseEntity>> sendCheckin(
      CheckinRequestEntity request);

  // Profile methods
  Future<Result<ProfileResponseEntity>> getProfile({
    required String token,
    required String userId,
    bool isSync = false,
  });

  // Misc Setting methods
  Future<Result<MiscSettingResponseEntity>> getMiscSetting({
    required String token,
    required String userId,
    bool isSync = false,
  });

  Future<Result<ProfileResponseEntity>> updateProfile({
    required UpdateProfileRequest request,
  });

  // Notification methods
  Future<Result<NotificationResponse>> getAlerts(
    NotificationReqParams request, {
    bool isSync = false,
  });

  // Vacancy methods
  Future<Result<List<VacancyEntity>>> getVacancies({
    required String token,
    required String userId,
  });

  Future<Result<bool>> applyToVacancy({
    required String token,
    required String userId,
    required int vacancyId,
  });

  Future<Result<bool>> referVacancy({
    required String token,
    required String userId,
    required int vacancyId,
    required String refereeEmail,
  });

  // Photo upload methods
  Future<Result<UploadPhotoResponseEntity>> uploadPhoto(
      UploadPhotoRequestEntity request);

  /// Upload photo with automatic response handling
  ///
  /// This method combines photo upload with duplicate detection and local storage optimization.
  /// It automatically processes the server response to handle duplicates and update local records.
  ///
  /// Parameters:
  /// - [request]: The photo upload request
  /// - [taskId]: The ID of the task containing the photo
  /// - [originalLocalPath]: The original local path of the photo being uploaded
  ///
  /// Returns the upload response if successful, or an error if failed
  Future<Result<UploadPhotoResponseEntity>> uploadPhotoWithHandling({
    required UploadPhotoRequestEntity request,
    required int taskId,
    required String originalLocalPath,
  });

  Future<Result<SyncPicResponseEntity>> syncPhotoInfo(
      SyncPicInfoRequestEntity request);

  /// Comprehensive photo upload and sync workflow
  ///
  /// This method performs a complete end-to-end photo synchronization:
  /// 1. Batch uploads all photos with automatic response handling
  /// 2. Syncs photo metadata after all uploads complete
  /// 3. Cleans up soft-deleted photos from database and local storage
  ///
  /// Parameters:
  /// - [tasks]: List of tasks containing photos to upload and sync
  ///
  /// Returns a result indicating overall success/failure of the workflow
  Future<Result<bool>> uploadAndSyncPhotos({
    required List<TaskDetail> tasks,
  });

  // Signature upload methods
  Future<Result<UploadSignatureResponseEntity>> uploadSignature(
      UploadSignRequestEntity request);

  /// Upload signature with automatic response handling
  ///
  /// This method combines signature upload with duplicate detection and local storage optimization.
  /// It automatically processes the server response to handle duplicates and update local records.
  ///
  /// Parameters:
  /// - [request]: The signature upload request
  /// - [taskId]: The ID of the task containing the signature
  /// - [originalLocalPath]: The original local path of the signature being uploaded
  ///
  /// Returns the upload response if successful, or an error if failed
  Future<Result<UploadSignatureResponseEntity>> uploadSignatureWithHandling({
    required UploadSignRequestEntity request,
    required int taskId,
    required String originalLocalPath,
  });

  Future<Result<SyncSignatureResponseEntity>> syncSignatureInfo(
      SyncSignInfoRequestEntity request);

  /// Upload and sync signatures workflow
  ///
  /// This method orchestrates the complete signature upload and sync process:
  /// 1. Batch upload all signatures with automatic response handling
  /// 2. Sync signature metadata after all uploads complete
  /// 3. Clean up soft-deleted signatures
  ///
  /// Parameters:
  /// - [tasks]: List of tasks containing signatures to upload and sync
  ///
  /// Returns `true` if the workflow completed successfully, or an error if failed
  Future<Result<bool>> uploadAndSyncSignatures({
    required List<TaskDetail> tasks,
  });

  Future<Result<bool>> referVacancyWithDetails({
    required String token,
    required String userId,
    required int vacancyId,
    required String name,
    required String email,
    required String phone,
    required String stateName,
    required String suburb,
    required String comment,
    required String preference,
  });

  // POS methods
  Future<Result<PosResponseEntity>> updatePos(UpdatePosRequestEntity request);

  // Emulate User methods
  Future<Result<EmulateUserResponseEntity>> getEmulateUsers(
      EmulateUserRequestEntity request);

  // Coverage Stores and Clients methods
  Future<Result<CoverageStoresResponse>> getCoverageStores({
    required String userId,
    required String token,
  });

  Future<Result<CoverageClientsResponse>> getCoverageClients({
    required String userId,
    required String token,
  });

  // User State methods
  Future<Result<UserStateResponseEntity>> getUserState({
    required UserStateRequestEntity request,
  });

  // Opened Doc methods
  Future<Result<OpenedDocResponseEntity>> sendOpenedDocs(
    OpenedDocRequestEntity request,
  );

  // Cache methods for emulation
  Future<void> saveHistory(HistoryResponse response);
  Future<HistoryResponse?> getCachedHistory();
  Future<void> saveAlerts(NotificationResponse response);
  Future<NotificationResponse?> getCachedAlerts();
  Future<void> saveAvailability(AvailabilityResponse response);
  Future<AvailabilityResponse?> getCachedAvailability();
  Future<void> saveLeave(LeaveResponse response);
  Future<LeaveResponse?> getCachedLeave();
  Future<void> saveSkills(SkillsResponse response);
  Future<SkillsResponse?> getCachedSkills();
  Future<void> saveInduction(InductionResponse response);
  Future<InductionResponse?> getCachedInduction();
}
