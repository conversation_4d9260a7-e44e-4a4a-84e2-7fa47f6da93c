import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:storetrack_app/config/routes/app_router.gr.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/config/themes/app_typography.dart';
import 'package:storetrack_app/core/utils/pos_status_utils.dart';
import 'package:storetrack_app/core/utils/snackbar_service.dart';
import 'package:storetrack_app/features/auth/presentation/widgets/app_button.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/pos/pos_cubit.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/empty_state.dart';
import 'package:storetrack_app/shared/widgets/confirm_dialog.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import '../widgets/pos_item_card.dart';

@RoutePage()
class PosPage extends StatefulWidget {
  final TaskDetail? task;

  const PosPage({
    super.key,
    this.task,
  });

  @override
  State<PosPage> createState() => _PosPageState();
}

class _PosPageState extends State<PosPage> {
  List<PosItem> posItems = [];
  String connoteValue = '';
  bool? posReceived; // null = no status, true = received, false = not received
  bool isLoading = false;
  PosStatusResult _posStatus = PosStatusResult.empty();

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _loadPosData();
  }

  void _initializeData() {
    if (widget.task != null) {
      posItems = widget.task!.posItems ?? [];
      connoteValue = widget.task!.connoteUrl ?? 'N/A';
      _loadPosData();
      if (posItems.isEmpty) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _showNoPosItemsDialog();
        });
      }
    } else {
      connoteValue = 'https://www.google.com';
      posItems = [
        PosItem(itemName: 'Sample Item 1', itemAmount: 5, photoUrl: null),
        PosItem(itemName: 'Sample Item 2', itemAmount: 3, photoUrl: null),
        PosItem(itemName: 'Sample Item 3', itemAmount: 8, photoUrl: null),
      ];
      posReceived = null;
      _posStatus = PosStatusResult(
        posReceivedCount: 0,
        totalPosItems: 3,
        isCompleted: false,
        hasAnyReceived: false,
      );
    }
  }

  void _loadPosData() {
    if (widget.task == null) return;

    setState(() {
      _posStatus = PosStatusUtils.calculatePosStatus(widget.task!);
      posReceived = _posStatus.hasAnyReceived;
      print('POS STATUS 123: ${_posStatus.hasAnyReceived}');
    });
  }

  void _showNoPosItemsDialog() {
    ConfirmDialog.show(
      context: context,
      title: 'No POS Items Found',
      message: 'Please try again later.',
      confirmText: 'OK',
      onConfirm: () {
        context.router.maybePop();
      },
    );
  }

  void _onTrackingPressed() {
    if (connoteValue.isNotEmpty && connoteValue != 'N/A') {
      try {
        final validUrl = _ensureValidUrl(connoteValue);
        context.router.push(WebBrowserRoute(url: validUrl, title: 'Tracking'));
      } catch (e) {
        SnackBarService.error(
          context: context,
          message: 'Invalid tracking URL: $connoteValue',
        );
      }
    } else {
      SnackBarService.warning(
        context: context,
        message: 'No tracking URL available',
      );
    }
  }

  void _onReceiveAllPressed() {
    _updatePosStatus(true);
  }

  void _onNotReceivedPressed() {
    _updatePosStatus(false);
  }

  void _updatePosStatus(bool received) {
    if (widget.task != null) {
      context.read<PosCubit>().updatePosStatus(
            task: widget.task!,
            received: received,
          );
    } else {
      SnackBarService.error(
        context: context,
        message: 'Task information not available',
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<PosCubit, PosState>(
      listener: (context, state) {
        setState(() {
          isLoading = state is PosLoading;
        });
        if (state is PosSuccess) {
          _loadPosData();
          SnackBarService.success(context: context, message: state.message);
        } else if (state is PosError) {
          SnackBarService.error(context: context, message: state.message);
        }
      },
      child: Scaffold(
        backgroundColor: AppColors.lightGrey1,
        appBar: CustomAppBar(
          title: 'POS Details',
          onBackPressed: () => context.router.maybePop(),
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Section 1: Shipment Details
              _buildSectionWrapper(
                title: 'Shipment Details',
                child: _buildConnoteSection(),
              ),

              // Section 2: Shipment Status & Actions
              _buildSectionWrapper(
                title: 'Shipment Status & Actions',
                child: _buildStatusAndActionsSection(),
              ),

              // Section 3: POS Items
              _buildSectionWrapper(
                title: 'POS Items',
                child: _buildPosItemsSection(),
              ),
              const SizedBox(height: 32), // Bottom spacing
            ],
          ),
        ),
      ),
    );
  }

  // A helper widget to create a consistent section layout
  Widget _buildSectionWrapper({required String title, required Widget child}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
          child: Text(
            title,
            style: AppTypography.montserratTitleSmall.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        Card(
          margin: const EdgeInsets.symmetric(horizontal: 16),
          elevation: 2,
          color: Colors.white,
          shadowColor: AppColors.black10,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: child,
        ),
      ],
    );
  }

  // --- SECTION 1: SHIPMENT DETAILS ---
  Widget _buildConnoteSection() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Connote Number:',
            style: AppTypography.montserratTitleExtraSmall.copyWith(
                // color: AppColors.blackTint1,
                ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                  decoration: BoxDecoration(
                    color: AppColors.lightGrey1,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: AppColors.blackTint2),
                  ),
                  child: Text(
                    connoteValue,
                    style: AppTypography.montserratTitleSmall.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppColors.primaryBlue,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              SizedBox(
                width: 100,
                child: AppButton(
                  text: 'Track',
                  color: AppColors.loginGreen,
                  onPressed: _onTrackingPressed,
                  height: 44,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // --- SECTION 2: SHIPMENT STATUS & ACTIONS ---
  Widget _buildStatusAndActionsSection() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          _buildPosStatusIndicator(),
          const SizedBox(height: 16),
          _buildActionButtons(),
        ],
      ),
    );
  }

  Widget _buildPosStatusIndicator() {
    final isCompleted = _posStatus.isCompleted;

    // Use better colors for readability
    final statusColor =
        isCompleted ? AppColors.loginGreen : AppColors.primaryBlue;
    final backgroundColor = isCompleted
        ? AppColors.loginGreen.withValues(alpha: 0.15)
        : AppColors.primaryBlue.withValues(alpha: 0.15);
    final borderColor =
        isCompleted ? AppColors.loginGreen : AppColors.primaryBlue;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: borderColor, width: 1),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            isCompleted ? Icons.check_circle : Icons.inventory_2_outlined,
            color: statusColor,
            size: 20,
          ),
          const SizedBox(width: 12),
          Text(
            'POS Status: ',
            style: AppTypography.montserratTitleSmall.copyWith(
              color: AppColors.black,
            ),
          ),
          Expanded(
            child: Text(
              PosStatusUtils.formatPosStatus(_posStatus),
              style: AppTypography.montserratTitleSmall.copyWith(
                color: statusColor,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.end,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    // Determine button colors and text based on POS status
    Color receiveButtonColor;
    Color notReceivedButtonColor;
    String receiveButtonText;
    String notReceivedButtonText;

    if (_posStatus.hasAnyReceived) {
      // POS has been received - show green "Received", grey "Not Received"
      receiveButtonColor = AppColors.loginGreen;
      notReceivedButtonColor = AppColors.blackTint2;
      receiveButtonText = 'Received';
      notReceivedButtonText = 'Not Received';
    } else if (_posStatus.totalPosItems > 0 && !_posStatus.hasAnyReceived) {
      // POS explicitly not received - show grey "Receive All", red "Not Received"
      receiveButtonColor = AppColors.blackTint2;
      notReceivedButtonColor = AppColors.loginRed;
      receiveButtonText = 'Receive All';
      notReceivedButtonText = 'Not Received';
    } else {
      // No clear status - show both buttons in grey
      receiveButtonColor = AppColors.blackTint2;
      notReceivedButtonColor = AppColors.blackTint2;
      receiveButtonText = 'Receive All';
      notReceivedButtonText = 'Not Received';
    }

    return Row(
      children: [
        Expanded(
          child: AppButton(
            text: isLoading ? 'Loading...' : receiveButtonText,
            color: receiveButtonColor,
            onPressed: isLoading ? () {} : _onReceiveAllPressed,
            height: 44,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: AppButton(
            text: isLoading ? 'Loading...' : notReceivedButtonText,
            color: notReceivedButtonColor,
            onPressed: isLoading ? () {} : _onNotReceivedPressed,
            height: 44,
          ),
        ),
      ],
    );
  }

  // --- SECTION 3: POS ITEMS ---
  Widget _buildPosItemsSection() {
    return ClipRRect(
      borderRadius: BorderRadius.circular(12),
      child: Column(
        children: [
          _buildTableHeader(),
          _buildPosItemsList(),
        ],
      ),
    );
  }

  Widget _buildTableHeader() {
    return Container(
      height: 44,
      padding: const EdgeInsets.symmetric(horizontal: 20),
      color: AppColors.blackTint2,
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: Text(
              'Item Name',
              style: AppTypography.montserratTitleExtraSmall.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          SizedBox(
            width: 70,
            child: Center(
              child: Text(
                'Qty',
                style: AppTypography.montserratTitleExtraSmall.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          SizedBox(
            width: 70,
            child: Center(
              child: Text(
                'Photo',
                style: AppTypography.montserratTitleExtraSmall.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPosItemsList() {
    if (posItems.isEmpty) {
      return const Padding(
        padding: EdgeInsets.symmetric(vertical: 24.0),
        child: EmptyState(message: 'No POS Items Found'),
      );
    }

    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: posItems.length,
      separatorBuilder: (context, index) => const Divider(
        height: 1,
        indent: 20,
        endIndent: 20,
      ),
      itemBuilder: (context, index) {
        return PosItemCard(
          posItem: posItems[index],
          onPhotoTap: () => _onPhotoTap(index),
        );
      },
    );
  }

  void _onPhotoTap(int index) {
    if (index >= 0 && index < posItems.length) {
      final posItem = posItems[index];
      if (posItem.photoUrl != null && posItem.photoUrl!.isNotEmpty) {
        try {
          final validUrl = _ensureValidUrl(posItem.photoUrl!);
          context.router
              .push(WebBrowserRoute(url: validUrl, title: 'POS Item Photo'));
        } catch (e) {
          SnackBarService.error(
            context: context,
            message: 'Invalid photo URL',
          );
        }
      } else {
        SnackBarService.info(
          context: context,
          message: 'No photo available for ${posItem.itemName ?? 'this item'}',
        );
      }
    } else {
      SnackBarService.error(
        context: context,
        message: 'Invalid item selected',
      );
    }
  }

  String _ensureValidUrl(String url) {
    if (url.startsWith('http://') || url.startsWith('https://')) {
      return url;
    }

    // If it's a numeric value or doesn't have a scheme, add https://
    if (RegExp(r'^\d+$').hasMatch(url)) {
      throw ArgumentError('Invalid URL: numeric value without scheme');
    }

    // For other cases without scheme, prepend https://
    return 'https://$url';
  }
}
