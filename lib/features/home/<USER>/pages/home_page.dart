import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/shared/widgets/tutorial_dialog.dart';
import 'package:storetrack_app/core/storage/data_manager.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/shared/cubits/sync_cubit.dart';
import 'package:storetrack_app/shared/widgets/confirm_dialog.dart';
import 'package:storetrack_app/core/database/realm_database.dart';
import 'package:storetrack_app/features/home/<USER>/models/address_skill_validation_model.dart';
import 'package:storetrack_app/features/home/<USER>/mappers/address_skill_validation_mapper.dart';

import '../../../../config/routes/app_router.gr.dart';
import '../../../../config/themes/app_colors.dart';
import '../../../../core/constants/app_assets.dart';

late TabsRouter tabsRouter;

@RoutePage()
class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  bool _hasCheckedTutorial = false;
  bool _isProfileValidationRequired = false;

  @override
  void initState() {
    super.initState();
    // Check tutorial setting after the widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkAndShowTutorial();
    });
  }

  Future<void> _checkAndShowTutorial() async {
    if (_hasCheckedTutorial) return;

    try {
      final dataManager = sl<DataManager>();
      final tutorialEnabled = await dataManager.getTutorialEnabled();

      if (tutorialEnabled && mounted) {
        _hasCheckedTutorial = true;
        await TutorialDialog.show(context);
      }
    } catch (e) {
      // Handle error silently
    }
  }

  // Check if the current route is the DashboardRoute
  bool _isDashboardRoute() {
    // First check if we're on the first tab (Dashboard tab)
    return context.router.currentPath == '/home-route';
  }

  /// Handle user state validation results
  Future<void> _handleUserStateValidation(
      SyncUserStateValidationRequired state) async {
    if (!mounted) return;

    try {
      switch (state.action) {
        case 'kickOut':
          // Show inactive user kick-out dialog
          await _showKickOutDialog(
            state.title,
            state.message,
            navigateToLogin: true,
          );
          break;

        case 'customMessage':
          // Show custom message dialog
          await _showCustomMessageDialog(
            state.title,
            state.message,
            kickOut: state.kickOut,
            navigateToLogin: state.navigateToLogin,
            checkAddressSkills: state.checkAddressSkills,
          );
          break;

        case 'redirectToProfile':
          // Navigate to profile tab and set validation flag
          setState(() {
            _isProfileValidationRequired = true;
          });
          if (mounted) {
            tabsRouter.setActiveIndex(1); // Profile tab index
          }
          break;
      }

      // Mark validation as handled
      if (mounted) {
        context.read<SyncCubit>().markUserStateValidationHandled();
      }
    } catch (e) {
      // Handle error silently or log
      if (mounted) {
        context.read<SyncCubit>().markUserStateValidationHandled();
      }
    }
  }

  /// Show kick-out dialog for inactive users
  Future<void> _showKickOutDialog(String title, String message,
      {required bool navigateToLogin}) async {
    if (!mounted) return;

    await ConfirmDialog.show(
      context: context,
      title: title,
      message: message,
      confirmText: 'OK',
      onConfirm: () async {
        if (navigateToLogin && mounted) {
          context.router.replaceAll([const LoginRoute()]);
        }
      },
      onCancel: null,
      isDismissible: false,
    );
  }

  /// Check if address/skills validation is complete and clear restriction if so
  Future<void> _checkAndClearValidationRestriction() async {
    try {
      final realm = sl<RealmDatabase>().realm;
      final validationModel =
          realm.all<AddressSkillValidationModel>().firstOrNull;

      if (validationModel == null) {
        return;
      }

      final validationEntity =
          AddressSkillValidationMapper.toEntity(validationModel);

      // Check if validation is complete
      final isValidationComplete = validationEntity.isAddressValidated &&
          validationEntity.isSkillValidated &&
          validationEntity.isAvailabilityValidated;

      if (isValidationComplete) {
        setState(() {
          _isProfileValidationRequired = false;
        });
      }
    } catch (e) {
      // Handle error silently
    }
  }

  /// Handle tab navigation with profile validation restriction
  void _handleTabTap(int index) {
    // If user is redirected to profile for validation, block dashboard access
    if (index == 0 && _isProfileValidationRequired) {
      // Show informative dialog instead of switching to dashboard
      ConfirmDialog.show(
        context: context,
        title: 'Profile Validation Required',
        message:
            'Please complete your profile validation before accessing the dashboard.',
        confirmText: 'OK',
        onConfirm: () {},
      );
      return;
    }

    // Special handling for dashboard tab (index 0)
    if (index == 0) {
      // If already on dashboard tab, navigate to main dashboard page
      if (tabsRouter.activeIndex == 0) {
        context.router.navigate(DashboardRoute());
      } else {
        tabsRouter.setActiveIndex(0);
      }
    } else {
      // Normal tab navigation for other tabs
      tabsRouter.setActiveIndex(index);
    }
  }

  /// Show custom message dialog with appropriate actions
  Future<void> _showCustomMessageDialog(
    String title,
    String message, {
    required bool kickOut,
    required bool navigateToLogin,
    required bool checkAddressSkills,
  }) async {
    if (!mounted) return;

    await ConfirmDialog.show(
      context: context,
      title: title,
      message: message,
      confirmText: 'OK',
      onConfirm: () async {
        if (kickOut && navigateToLogin && mounted) {
          // Navigate to login for kick-out scenarios
          context.router.replaceAll([const LoginRoute()]);
        } else if (checkAddressSkills && mounted) {
          // Navigate to profile tab for address/skills validation
          tabsRouter.setActiveIndex(1); // Profile tab index
        }
      },
      onCancel: null,
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<SyncCubit, SyncState>(
      listener: (context, state) {
        if (state is SyncUserStateValidationRequired) {
          _handleUserStateValidation(state);
        } else if (state is SyncSuccess) {
          // Only clear profile validation restriction if validation is actually complete
          if (_isProfileValidationRequired) {
            _checkAndClearValidationRestriction();
          }
        }
      },
      child: AutoTabsRouter(
        routes: const [
          DashboardHolderRoute(),
          // AssistantRoute(),
          ProfileHolderRoute(),
          MoreHolderRoute()
        ],
        builder: (context, child) {
          tabsRouter = AutoTabsRouter.of(context);

          final textTheme = Theme.of(context).textTheme;
          return Scaffold(
            body: child,
            appBar: AppBar(
              backgroundColor: Colors.transparent,
              elevation: 0,
              toolbarHeight: 0,
              forceMaterialTransparency: true,
              systemOverlayStyle: SystemUiOverlayStyle(
                statusBarColor: Colors.transparent,
                statusBarIconBrightness:
                    _isDashboardRoute() ? Brightness.light : Brightness.dark,
                systemNavigationBarColor: Colors.transparent,
                systemNavigationBarIconBrightness: Brightness.light,
                statusBarBrightness:
                    _isDashboardRoute() ? Brightness.dark : Brightness.light,
              ),
            ),
            extendBodyBehindAppBar: true,
            bottomNavigationBar: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 8,
                    offset: const Offset(0, -2),
                  ),
                ],
              ),
              child: BottomNavigationBar(
                  type: BottomNavigationBarType.fixed,
                  backgroundColor: Colors.white,
                  currentIndex: tabsRouter.activeIndex,
                  onTap: _handleTabTap,
                  selectedFontSize: 12,
                  unselectedItemColor: AppColors.black.withValues(alpha: .4),
                  selectedItemColor: AppColors.primaryBlue,
                  selectedLabelStyle: textTheme.montserratBottomNavigation,
                  unselectedLabelStyle: textTheme.montserratBottomNavigation
                      .copyWith(color: AppColors.black.withValues(alpha: .6)),
                  items: [
                    BottomNavigationBarItem(
                      icon: Padding(
                        padding: const EdgeInsets.only(bottom: 4),
                        child: tabsRouter.activeIndex == 0
                            ? Image.asset(
                                color: AppColors.primaryBlue,
                                AppAssets.homeDashboard,
                                width: 18,
                              )
                            : Image.asset(
                                color: AppColors.black.withValues(alpha: .6),
                                AppAssets.homeDashboard,
                                width: 18,
                              ),
                      ),
                      label: 'Dashboard',
                    ),
                    // BottomNavigationBarItem(
                    //   icon: Padding(
                    //     padding: const EdgeInsets.only(bottom: 4),
                    //     child: tabsRouter.activeIndex == 1
                    //         ? Image.asset(
                    //             color: AppColors.primaryBlue,
                    //             AppAssets.homeAssistant,
                    //             width: 18,
                    //           )
                    //         : Image.asset(
                    //             color: AppColors.black.withValues(alpha:.6),
                    //             AppAssets.homeAssistant,
                    //             width: 18,
                    //           ),
                    //   ),
                    //   label: 'Assistant',
                    // ),
                    BottomNavigationBarItem(
                      icon: Padding(
                        padding: const EdgeInsets.only(bottom: 4),
                        child: tabsRouter.activeIndex == 1
                            ? Image.asset(
                                color: AppColors.primaryBlue,
                                AppAssets.homeProfile,
                                width: 18,
                              )
                            : Image.asset(
                                color: AppColors.black.withValues(alpha: .6),
                                AppAssets.homeProfile,
                                width: 18,
                              ),
                      ),
                      label: 'Profile',
                    ),
                    BottomNavigationBarItem(
                      icon: Padding(
                        padding: const EdgeInsets.only(bottom: 8),
                        child: tabsRouter.activeIndex == 2
                            ? Image.asset(
                                color: AppColors.primaryBlue,
                                AppAssets.homeMore,
                                width: 18,
                              )
                            : Image.asset(
                                color: AppColors.black.withValues(alpha: .6),
                                AppAssets.homeMore,
                                width: 18,
                              ),
                      ),
                      label: 'More',
                    ),
                  ]
                  // BottomNavigationBarItem(
                  //   icon: Icon(Icons.more_horiz),
                  //   label: 'More',
                  // ),
                  // ],
                  ),
            ),
          );
        },
      ),
    );
  }
}
