import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/routes/app_router.gr.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/core/utils/form_utils.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/empty_state.dart';
import 'package:storetrack_app/core/database/realm_database.dart';
import 'package:storetrack_app/features/home/<USER>/models/task_detail_model.dart';

@RoutePage()
class FQPDPage extends StatefulWidget {
  final num? questionId;
  final num? taskId;
  final num? formId;

  const FQPDPage({
    super.key,
    this.questionId,
    this.taskId,
    this.formId,
  });

  @override
  State<FQPDPage> createState() => _FQPDPageState();
}

class _FQPDPageState extends State<FQPDPage> {
  // Debug key for logging
  static const String debugKey = 'FQPD_DEBUG';

  // Current question fetched from database
  Question? _question;

  // Loading state
  bool _isLoading = true;
  String? _errorMessage;

  // Available question parts from the ORIGINAL question (never modified)
  List<QuestionPart> get _availableQuestionParts =>
      _question?.questionParts ?? [];

  // Filtered list of original question parts for bottom sheets
  List<QuestionPart> get _originalQuestionPartsForSelection {
    return _availableQuestionParts
        .where((qp) =>
            qp.questionpartMultiId == null ||
            !qp.questionpartMultiId!.contains('-'))
        .toList();
  }

  // List of selected question parts for non-restrictedMultiQuestion
  final List<QuestionPart> _selectedQuestionParts = [];

  // List of template question parts for restrictedMultiQuestion
  final List<QuestionPart?> _templateQuestionParts = [];

  // Track if this is a restrictedMultiQuestion
  bool get _isRestrictedMultiQuestion {
    return _question?.isMulti == true &&
        _question?.multiMeasurementId != null &&
        _question?.multiMeasurementId != 0;
  }

  // Track completion status for question parts
  final Map<String, bool> _completionStatus = {};

  @override
  void initState() {
    super.initState();
    debugPrint('[$debugKey] initState - Starting FQPD page initialization');
    _loadQuestionFromDatabase();
  }

  /// Load question from database using the provided IDs
  Future<void> _loadQuestionFromDatabase() async {
    debugPrint(
        '[$debugKey] _loadQuestionFromDatabase - Starting with taskId: ${widget.taskId}, formId: ${widget.formId}, questionId: ${widget.questionId}');

    if (widget.taskId == null ||
        widget.formId == null ||
        widget.questionId == null) {
      debugPrint(
          '[$debugKey] _loadQuestionFromDatabase - ERROR: Missing required IDs');
      setState(() {
        _errorMessage = 'Missing required IDs: taskId, formId, or questionId';
        _isLoading = false;
      });
      return;
    }

    try {
      final realm = RealmDatabase.instance.realm;

      // Find the task with the matching taskId
      final taskModel = realm.query<TaskDetailModel>(
          'taskId == \$0', [widget.taskId!.toInt()]).firstOrNull;

      if (taskModel == null) {
        debugPrint(
            '[$debugKey] _loadQuestionFromDatabase - ERROR: Task not found for taskId: ${widget.taskId}');
        setState(() {
          _errorMessage = 'Task not found for taskId: ${widget.taskId}';
          _isLoading = false;
        });
        return;
      }

      // Find the form with the matching formId
      final formModel = taskModel.forms
          .where((form) => form.formId == widget.formId!.toInt())
          .firstOrNull;

      if (formModel == null) {
        debugPrint(
            '[$debugKey] _loadQuestionFromDatabase - ERROR: Form not found for formId: ${widget.formId}');
        setState(() {
          _errorMessage = 'Form not found for formId: ${widget.formId}';
          _isLoading = false;
        });
        return;
      }

      // Find the question with the matching questionId
      final questionModel = formModel.questions
          .where(
              (question) => question.questionId == widget.questionId!.toInt())
          .firstOrNull;

      if (questionModel == null) {
        debugPrint(
            '[$debugKey] _loadQuestionFromDatabase - ERROR: Question not found for questionId: ${widget.questionId}');
        setState(() {
          _errorMessage =
              'Question not found for questionId: ${widget.questionId}';
          _isLoading = false;
        });
        return;
      }

      debugPrint(
          '[$debugKey] _loadQuestionFromDatabase - Found question with ${questionModel.questionParts.length} original question parts');

      // Convert QuestionModel to Question entity using mapper
      final questionEntity = Question(
        questionId: questionModel.questionId,
        questionOrderId: questionModel.questionOrderId,
        questionDescription: questionModel.questionDescription,
        isComment: questionModel.isComment,
        isCommentMandatory: questionModel.isCommentMandatory,
        showQuestions: questionModel.showQuestions,
        hasSignature: questionModel.hasSignature,
        isSignatureMandatory: questionModel.isSignatureMandatory,
        signatureUrl: questionModel.signatureUrl,
        photoUrl: questionModel.photoUrl,
        questionBrief: questionModel.questionBrief,
        questionParts: questionModel.questionParts
            .map((qp) => QuestionPart(
                  projectid: qp.projectid,
                  questionpartId: qp.questionpartId,
                  questionpartDescription: qp.questionpartDescription,
                  price: qp.price,
                  modifiedTimeStampQuestionpart:
                      qp.modifiedTimeStampQuestionpart,
                  targetByCycle: qp.targetByCycle,
                  targetByGroup: qp.targetByGroup,
                  targetByCompany: qp.targetByCompany,
                  targetByRegion: qp.targetByRegion,
                  targetByBudget: qp.targetByBudget,
                  osaForm: qp.osaForm,
                  companyId: qp.companyId,
                  itemImage: qp.itemImage,
                  targeted: qp.targeted,
                ))
            .toList(),
        measurements: questionModel.measurements
            .map((m) => Measurement(
                  measurementId: m.measurementId,
                  measurementOrderId: m.measurementOrderId,
                  measurementTypeId: m.measurementTypeId,
                  measurementDescription: m.measurementDescription,
                  required: m.required,
                  measurementOptions: m.measurementOptions
                      .map((mo) => MeasurementOption(
                            measurementId: mo.measurementId,
                            measurementOptionId: mo.measurementOptionId,
                            measurementOptionDescription:
                                mo.measurementOptionDescription,
                            modifiedTimeStampMeasurementoption:
                                mo.modifiedTimeStampMeasurementoption,
                            budgetOffset: mo.budgetOffset,
                            budgetOffsetType: mo.budgetOffsetType,
                            isAnswer: mo.isAnswer,
                          ))
                      .toList(),
                  measurementConditions: m.measurementConditions
                      .map((mc) => MeasurementCondition(
                            measurementId: mc.measurementId,
                            measurementOptionId: mc.measurementOptionId,
                            actionMeasurementId: mc.actionMeasurementId,
                            action: mc.action,
                            modifiedTimeStampMeasurementconidtion:
                                mc.modifiedTimeStampMeasurementconidtion,
                          ))
                      .toList(),
                  // Add other basic measurement fields as needed
                  measurementTypeName: m.measurementTypeName,
                  defaultAction: m.defaultAction,
                  measurementDefaultsResult: m.measurementDefaultsResult,
                  modifiedTimeStampMeasurement: m.modifiedTimeStampMeasurement,
                  modifiedTimeStampMeasurementDefaultsResult:
                      m.modifiedTimeStampMeasurementDefaultsResult,
                  mandatoryPhototypesCount: m.mandatoryPhototypesCount,
                  optionalPhototypesCount: m.optionalPhototypesCount,
                  measurementImage: m.measurementImage,
                  companyid: m.companyid,
                  modifiedTimeStampMeasurementvalidation:
                      m.modifiedTimeStampMeasurementvalidation,
                  validationTypeId: m.validationTypeId,
                  rangeValidation: m.rangeValidation,
                  expressionValidation: m.expressionValidation,
                  errorMessage: m.errorMessage,
                  modifiedTimeStampMeasurementdefault:
                      m.modifiedTimeStampMeasurementdefault,
                ))
            .toList(),
        questionConditions: questionModel.questionConditions
            .map((qc) => QuestionCondition(
                  measurementId: qc.measurementId,
                  measurementOptionId: qc.measurementOptionId,
                  actionQuestionId: qc.actionQuestionId,
                  action: qc.action,
                  modifiedTimeStampQuestioncondition:
                      qc.modifiedTimeStampQuestioncondition,
                ))
            .toList(),
        commentTypes: questionModel.commentTypes
            .map((ct) => CommentType(
                  commentTypeId: ct.commentTypeId,
                  commentType: ct.commentType,
                ))
            .toList(),
        modifiedTimeStampQuestion: questionModel.modifiedTimeStampQuestion,
        targetByCycle: questionModel.targetByCycle,
        targetByGroup: questionModel.targetByGroup,
        targetByCompany: questionModel.targetByCompany,
        targetByRegion: questionModel.targetByRegion,
        targetByBudget: questionModel.targetByBudget,
        isMll: questionModel.isMll,
        photoTagsTwo: questionModel.photoTagsTwo
            .map((pt) => PhotoTagsT(
                  questionpartId: pt.questionpartId,
                  measurementId: pt.measurementId,
                  isMandatory: pt.isMandatory,
                  photoResPerc: pt.photoResPerc,
                  liveImagesOnly: pt.liveImagesOnly,
                  photoTagId: pt.photoTagId,
                  photoTag: pt.photoTag,
                  numberOfPhotos: pt.numberOfPhotos,
                  measurementPhototypeId: pt.measurementPhototypeId,
                  imageRec: pt.imageRec,
                  userPhotos: pt.userPhotos
                      .map((up) => Photo(
                            formId: up.formId,
                            questionId: up.questionId,
                            measurementId: up.measurementId,
                            folderId: up.folderId,
                            photoId: up.photoId,
                            photoUrl: up.photoUrl,
                            thumbnailUrl: up.thumbnailUrl,
                            caption: up.caption,
                            modifiedTimeStampPhoto: up.modifiedTimeStampPhoto,
                            cannotUploadMandatory: up.cannotUploadMandatory,
                            userDeletedPhoto: up.userDeletedPhoto,
                            imageRec: up.imageRec,
                            questionpartId: up.questionpartId,
                            questionPartMultiId: up.questionPartMultiId,
                            measurementPhototypeId: up.measurementPhototypeId,
                            combineTypeId: up.combineTypeId,
                            photoTagId: up.photoTagId,
                            photoCombinetypeId: up.photoCombinetypeId,
                          ))
                      .toList(),
                ))
            .toList(),
        photoTagsThree: questionModel.photoTagsThree
            .map((pt) => PhotoTagsT(
                  questionpartId: pt.questionpartId,
                  measurementId: pt.measurementId,
                  isMandatory: pt.isMandatory,
                  photoResPerc: pt.photoResPerc,
                  liveImagesOnly: pt.liveImagesOnly,
                  photoTagId: pt.photoTagId,
                  photoTag: pt.photoTag,
                  numberOfPhotos: pt.numberOfPhotos,
                  measurementPhototypeId: pt.measurementPhototypeId,
                  imageRec: pt.imageRec,
                  userPhotos: pt.userPhotos
                      .map((up) => Photo(
                            formId: up.formId,
                            questionId: up.questionId,
                            measurementId: up.measurementId,
                            folderId: up.folderId,
                            photoId: up.photoId,
                            photoUrl: up.photoUrl,
                            thumbnailUrl: up.thumbnailUrl,
                            caption: up.caption,
                            modifiedTimeStampPhoto: up.modifiedTimeStampPhoto,
                            cannotUploadMandatory: up.cannotUploadMandatory,
                            userDeletedPhoto: up.userDeletedPhoto,
                            imageRec: up.imageRec,
                            questionpartId: up.questionpartId,
                            questionPartMultiId: up.questionPartMultiId,
                            measurementPhototypeId: up.measurementPhototypeId,
                            combineTypeId: up.combineTypeId,
                            photoTagId: up.photoTagId,
                            photoCombinetypeId: up.photoCombinetypeId,
                          ))
                      .toList(),
                ))
            .toList(),
        isMulti: questionModel.isMulti,
        multiMeasurementId: questionModel.multiMeasurementId,
        isMultiOneAnswer: questionModel.isMultiOneAnswer,
        flip: questionModel.flip,
        questionTypeId: questionModel.questionTypeId,
      );

      debugPrint(
          '[$debugKey] _loadQuestionFromDatabase - Question entity created successfully. isMulti: ${questionEntity.isMulti}, multiMeasurementId: ${questionEntity.multiMeasurementId}');
      debugPrint(
          '[$debugKey] _loadQuestionFromDatabase - Is restricted multi question: $_isRestrictedMultiQuestion');

      setState(() {
        _question = questionEntity;
        _isLoading = false;
      });

      // Initialize question parts after loading
      _initializeQuestionParts();
      await _loadSavedQuestionParts();

      // Log current state for debugging
      _logCurrentState();
    } catch (e) {
      debugPrint(
          '[$debugKey] _loadQuestionFromDatabase - EXCEPTION: ${e.toString()}');
      setState(() {
        _errorMessage = 'Error loading question: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  /// Initialize question parts based on question type
  void _initializeQuestionParts() {
    debugPrint(
        '[$debugKey] _initializeQuestionParts - Starting initialization. Is restricted: $_isRestrictedMultiQuestion');

    if (_isRestrictedMultiQuestion) {
      _initializeRestrictedMultiQuestion();
    } else {
      debugPrint(
          '[$debugKey] _initializeQuestionParts - Non-restricted mode: _selectedQuestionParts starts empty');
    }
    // For non-restrictedMultiQuestion, _selectedQuestionParts starts empty
  }

  /// Initialize restrictedMultiQuestion with saved value count
  void _initializeRestrictedMultiQuestion() {
    debugPrint(
        '[$debugKey] _initializeRestrictedMultiQuestion - Starting restricted multi question initialization');

    // Get the expected count from other questions
    final savedValueCount = _getSavedValueCountForRestrictedMultiQuestion();
    final expectedCount = savedValueCount > 0 ? savedValueCount : 1;

    debugPrint(
        '[$debugKey] _initializeRestrictedMultiQuestion - Expected count: $expectedCount, saved value count: $savedValueCount');

    // Check if there are already existing question parts with proper questionpartMultiId
    final existingQuestionParts = _getExistingQuestionPartsForRestrictedMulti();

    if (existingQuestionParts.isNotEmpty) {
      debugPrint(
          '[$debugKey] _initializeRestrictedMultiQuestion - Found ${existingQuestionParts.length} existing question parts');

      // Check if the existing count matches the expected count
      if (existingQuestionParts.length == expectedCount) {
        // Use existing question parts - they match the expected count
        debugPrint(
            '[$debugKey] _initializeRestrictedMultiQuestion - Existing count matches expected count, using existing question parts');
        _templateQuestionParts.clear();
        _templateQuestionParts.addAll(existingQuestionParts);
        return;
      } else if (existingQuestionParts.length > expectedCount) {
        // Too many existing parts - remove the excess ones
        debugPrint(
            '[$debugKey] _initializeRestrictedMultiQuestion - Too many existing parts (${existingQuestionParts.length} > $expectedCount), removing excess');
        _removeExcessQuestionParts(existingQuestionParts, expectedCount);

        // Use only the first expectedCount parts
        _templateQuestionParts.clear();
        _templateQuestionParts
            .addAll(existingQuestionParts.take(expectedCount));
        return;
      } else {
        // Too few existing parts - keep existing and add empty slots
        debugPrint(
            '[$debugKey] _initializeRestrictedMultiQuestion - Too few existing parts (${existingQuestionParts.length} < $expectedCount), adding empty slots');
        _templateQuestionParts.clear();
        _templateQuestionParts.addAll(existingQuestionParts);

        // Add empty slots for the remaining count
        final remainingCount = expectedCount - existingQuestionParts.length;
        for (int i = 0; i < remainingCount; i++) {
          _templateQuestionParts.add(null);
        }
        return;
      }
    }

    // No existing question parts found, create new template slots based on expected count
    debugPrint(
        '[$debugKey] _initializeRestrictedMultiQuestion - No existing data found, creating $expectedCount new template slots');

    // Initialize template question parts list with the exact count
    _templateQuestionParts.clear();
    for (int i = 0; i < expectedCount; i++) {
      _templateQuestionParts.add(null); // null means no selection yet
    }
  }

  /// Delete all existing question parts for restrictedMultiQuestion
  void _deleteAllExistingQuestionPartsForRestrictedMulti() {
    if (widget.taskId == null ||
        widget.formId == null ||
        _question?.questionId == null) {
      return;
    }

    try {
      final realm = RealmDatabase.instance.realm;

      // Find the task with the matching taskId
      final taskModel = realm.query<TaskDetailModel>(
          'taskId == \$0', [widget.taskId!.toInt()]).firstOrNull;

      if (taskModel == null) {
        debugPrint('Task not found in database for taskId: ${widget.taskId}');
        return;
      }

      // Find the form with the matching formId
      final formModel = taskModel.forms
          .where((form) => form.formId == widget.formId!.toInt())
          .firstOrNull;

      if (formModel == null) {
        debugPrint('Form not found in task for formId: ${widget.formId}');
        return;
      }

      realm.write(() {
        // Remove all QuestionAnswer records for this question
        final answersToRemove = formModel.questionAnswers
            .where((qa) => qa.questionId == _question!.questionId!.toInt())
            .toList();

        for (final answer in answersToRemove) {
          formModel.questionAnswers.remove(answer);
        }

        debugPrint(
            'Deleted ${answersToRemove.length} QuestionAnswer records for restrictedMultiQuestion');
      });
    } catch (e) {
      debugPrint(
          'Error deleting existing question parts for restrictedMultiQuestion: $e');
    }
  }

  /// Remove excess question parts when the expected count is lower than existing count
  void _removeExcessQuestionParts(
      List<QuestionPart?> existingQuestionParts, int expectedCount) {
    if (expectedCount >= existingQuestionParts.length) {
      return; // No excess to remove
    }

    // Remove the excess question parts from the end of the list
    final excessParts = existingQuestionParts.skip(expectedCount).toList();

    for (final excessPart in excessParts) {
      if (excessPart?.questionpartMultiId != null) {
        debugPrint(
            'Removing excess question part with multiId: ${excessPart!.questionpartMultiId}');
        _removeQuestionAnswerDataForQuestionPartMultiId(
            excessPart.questionpartMultiId);
      }
    }

    // Remove the excess items from the list
    existingQuestionParts.removeRange(
        expectedCount, existingQuestionParts.length);

    debugPrint('Removed ${excessParts.length} excess question parts');
  }

  /// Get existing question parts for restrictedMultiQuestion that have proper questionpartMultiId format
  /// Returns a list of nullable QuestionPart objects for template slots
  List<QuestionPart?> _getExistingQuestionPartsForRestrictedMulti() {
    if (widget.taskId == null ||
        widget.formId == null ||
        _question?.questionId == null) {
      return [];
    }

    try {
      final realm = RealmDatabase.instance.realm;

      // Find the task with the matching taskId
      final taskModel = realm.query<TaskDetailModel>(
          'taskId == \$0', [widget.taskId!.toInt()]).firstOrNull;

      if (taskModel == null) {
        debugPrint('Task not found in database for taskId: ${widget.taskId}');
        return [];
      }

      // Find the form with the matching formId
      final formModel = taskModel.forms
          .where((form) => form.formId == widget.formId!.toInt())
          .firstOrNull;

      if (formModel == null) {
        debugPrint('Form not found in task for formId: ${widget.formId}');
        return [];
      }

      // Find saved question answers for this question
      final savedAnswers = formModel.questionAnswers
          .where(
              (answer) => answer.questionId == _question!.questionId!.toInt())
          .toList();

      if (savedAnswers.isEmpty) {
        debugPrint(
            'No saved answers found for question ${_question!.questionId}');
        return [];
      }

      // Check for existing question parts with proper questionpartMultiId format
      final existingQuestionParts = <QuestionPart?>[];
      final processedMultiIds = <String>{};

      for (final answer in savedAnswers) {
        if (answer.questionpartId != null &&
            answer.questionPartMultiId != null) {
          final multiId = answer.questionPartMultiId!;

          // Skip if we've already processed this multiId
          if (processedMultiIds.contains(multiId)) {
            continue;
          }

          // Check if the multiId has the format "a-b" where "a" equals questionpartId
          final parts = multiId.split('-');
          if (parts.length >= 2) {
            final aPart = parts[0];
            final questionPartIdStr = answer.questionpartId.toString();

            if (aPart == questionPartIdStr) {
              // This is a valid existing question part
              processedMultiIds.add(multiId);

              // Find the question part by ID
              final questionPart = _availableQuestionParts
                  .where((qp) => qp.questionpartId == answer.questionpartId)
                  .firstOrNull;

              if (questionPart != null) {
                // Create a copy with the multiId from the database
                final existingQuestionPart = QuestionPart(
                  projectid: questionPart.projectid,
                  questionpartId: questionPart.questionpartId,
                  questionpartDescription: questionPart.questionpartDescription,
                  price: questionPart.price,
                  modifiedTimeStampQuestionpart:
                      questionPart.modifiedTimeStampQuestionpart,
                  targetByCycle: questionPart.targetByCycle,
                  targetByGroup: questionPart.targetByGroup,
                  targetByCompany: questionPart.targetByCompany,
                  targetByRegion: questionPart.targetByRegion,
                  targetByBudget: questionPart.targetByBudget,
                  osaForm: questionPart.osaForm,
                  companyId: questionPart.companyId,
                  itemImage: questionPart.itemImage,
                  targeted: questionPart.targeted,
                  questionpartMultiId: multiId,
                );
                existingQuestionParts.add(existingQuestionPart);
              }
            }
          }
        }
      }

      debugPrint(
          'Found ${existingQuestionParts.length} existing question parts with proper questionpartMultiId format');
      return existingQuestionParts;
    } catch (e) {
      debugPrint(
          'Error getting existing question parts for restrictedMultiQuestion: $e');
      return [];
    }
  }

  /// Get saved value count for restrictedMultiQuestion from database
  /// This should look at counter values from OTHER questions (not the restrictedMultiQuestion itself)
  int _getSavedValueCountForRestrictedMultiQuestion() {
    if (widget.taskId == null || _question?.questionId == null) {
      return 0;
    }

    try {
      final realm = RealmDatabase.instance.realm;

      // Find the task with the matching taskId
      final taskModel = realm.query<TaskDetailModel>(
          'taskId == \$0', [widget.taskId!.toInt()]).firstOrNull;

      if (taskModel == null) {
        debugPrint('Task not found in database for taskId: ${widget.taskId}');
        return 0;
      }

      // Find the form with the matching formId
      final formModel = taskModel.forms
          .where((form) => form.formId == widget.formId!.toInt())
          .firstOrNull;

      if (formModel == null) {
        debugPrint('Form not found in task for formId: ${widget.formId}');
        return 0;
      }

      // We need to get all questions from the form to find OTHER questions
      // Since we don't have direct access to the form entity here, we'll need to
      // look at all question answers and find the maximum counter value from
      // questions that are NOT this restrictedMultiQuestion

      int maxCount = 0;

      // Get all question answers for this form
      final allQuestionAnswers = formModel.questionAnswers.toList();

      // Group by questionId to find counter values from other questions
      final questionGroups = <int, List<QuestionAnswerModel>>{};
      for (final answer in allQuestionAnswers) {
        if (answer.questionId != null) {
          questionGroups.putIfAbsent(answer.questionId!, () => []).add(answer);
        }
      }

      // Check each question group (excluding the restrictedMultiQuestion itself)
      for (final entry in questionGroups.entries) {
        final questionId = entry.key;
        final answers = entry.value;

        // Skip the restrictedMultiQuestion itself
        if (questionId == _question!.questionId!.toInt()) {
          continue;
        }

        // Look for counter values in this question's answers
        for (final answer in answers) {
          if (answer.measurementTextResult != null) {
            final counterValue = int.tryParse(answer.measurementTextResult!);
            if (counterValue != null && counterValue > maxCount) {
              maxCount = counterValue;
              debugPrint(
                  'Found counter value $counterValue in other question $questionId');
            }
          }
        }
      }

      debugPrint(
          'Found max counter value from OTHER questions for restrictedMultiQuestion: $maxCount');
      return maxCount;
    } catch (e) {
      debugPrint(
          'Error getting saved value count for restrictedMultiQuestion: $e');
      return 0;
    }
  }

  /// Load saved question parts from database
  Future<void> _loadSavedQuestionParts() async {
    if (widget.taskId == null ||
        widget.formId == null ||
        _question?.questionId == null) {
      return;
    }

    try {
      final realm = RealmDatabase.instance.realm;

      // Find the task with the matching taskId
      final taskModel = realm.query<TaskDetailModel>(
          'taskId == \$0', [widget.taskId!.toInt()]).firstOrNull;

      if (taskModel == null) {
        debugPrint('Task not found in database for taskId: ${widget.taskId}');
        return;
      }

      // Find the form with the matching formId
      final formModel = taskModel.forms
          .where((form) => form.formId == widget.formId!.toInt())
          .firstOrNull;

      if (formModel == null) {
        debugPrint('Form not found in task for formId: ${widget.formId}');
        return;
      }

      // Find saved question answers for this question
      final savedAnswers = formModel.questionAnswers
          .where(
              (answer) => answer.questionId == _question!.questionId!.toInt())
          .toList();

      if (savedAnswers.isEmpty) {
        debugPrint(
            'No saved answers found for question ${_question!.questionId}');
        return;
      }

      // Process saved answers based on question type
      if (_isRestrictedMultiQuestion) {
        _loadRestrictedMultiQuestionData(savedAnswers);
      } else {
        _loadNonRestrictedMultiQuestionData(savedAnswers);
      }

      // Update completion status after loading data
      await _updateCompletionStatus();

      setState(() {});
    } catch (e) {
      debugPrint('Error loading saved question parts: $e');
    }
  }

  /// Load data for restrictedMultiQuestion
  void _loadRestrictedMultiQuestionData(
      List<QuestionAnswerModel> savedAnswers) {
    for (int i = 0; i < _templateQuestionParts.length; i++) {
      logger(
          'Template question part at index $i: ${_templateQuestionParts[i]}');
    }
    // For restrictedMultiQuestion, load the selected question parts into template slots
    for (final answer in savedAnswers) {
      if (answer.questionpartId != null) {
        // Find the question part by ID
        final questionPart = _availableQuestionParts
            .where(
              (qp) =>
                  qp.questionpartId == answer.questionpartId &&
                  qp.questionpartMultiId == answer.questionPartMultiId,
            )
            .firstOrNull;

        if (questionPart != null) {
          // Find an empty template slot and assign this question part
          for (int i = 0; i < _templateQuestionParts.length; i++) {
            if (_templateQuestionParts[i] == null) {
              // Create a copy with the multiId from the database
              final savedQuestionPart = QuestionPart(
                projectid: questionPart.projectid,
                questionpartId: questionPart.questionpartId,
                questionpartDescription: questionPart.questionpartDescription,
                price: questionPart.price,
                modifiedTimeStampQuestionpart:
                    questionPart.modifiedTimeStampQuestionpart,
                targetByCycle: questionPart.targetByCycle,
                targetByGroup: questionPart.targetByGroup,
                targetByCompany: questionPart.targetByCompany,
                targetByRegion: questionPart.targetByRegion,
                targetByBudget: questionPart.targetByBudget,
                osaForm: questionPart.osaForm,
                companyId: questionPart.companyId,
                itemImage: questionPart.itemImage,
                targeted: questionPart.targeted,
                questionpartMultiId: answer.questionPartMultiId, // Key change
              );
              _templateQuestionParts[i] = savedQuestionPart;
              break;
            }
          }
        }
      }
    }
  }

  /// Load data for non-restrictedMultiQuestion
  void _loadNonRestrictedMultiQuestionData(
      List<QuestionAnswerModel> savedAnswers) {
    debugPrint(
        '[$debugKey] _loadNonRestrictedMultiQuestionData - Loading ${savedAnswers.length} saved answers');

    // For non-restrictedMultiQuestion, load selected question parts
    // We need to look at questionPartMultiId to handle "a-b" format correctly
    final processedMultiIds = <String>{};

    for (final answer in savedAnswers) {
      debugPrint(
          '[$debugKey] _loadNonRestrictedMultiQuestionData - Processing answer: questionpartId=${answer.questionpartId}, multiId=${answer.questionPartMultiId}');

      if (answer.questionpartId != null && answer.questionPartMultiId != null) {
        final multiId = answer.questionPartMultiId!;

        // Skip if we've already processed this multiId
        if (processedMultiIds.contains(multiId)) {
          debugPrint(
              '[$debugKey] _loadNonRestrictedMultiQuestionData - Skipping duplicate multiId: $multiId');
          continue;
        }
        processedMultiIds.add(multiId);

        // Find the question part by ID from the ORIGINAL available question parts
        final questionPart = _availableQuestionParts
            .where((qp) => qp.questionpartId == answer.questionpartId)
            .firstOrNull;

        if (questionPart != null) {
          debugPrint(
              '[$debugKey] _loadNonRestrictedMultiQuestionData - Found original questionpart for ID: ${answer.questionpartId}');

          // Create a copy with the multiId from the database
          final savedQuestionPart = QuestionPart(
            projectid: questionPart.projectid,
            questionpartId: questionPart.questionpartId,
            questionpartDescription: questionPart.questionpartDescription,
            price: questionPart.price,
            modifiedTimeStampQuestionpart:
                questionPart.modifiedTimeStampQuestionpart,
            targetByCycle: questionPart.targetByCycle,
            targetByGroup: questionPart.targetByGroup,
            targetByCompany: questionPart.targetByCompany,
            targetByRegion: questionPart.targetByRegion,
            targetByBudget: questionPart.targetByBudget,
            osaForm: questionPart.osaForm,
            companyId: questionPart.companyId,
            itemImage: questionPart.itemImage,
            targeted: questionPart.targeted,
            questionpartMultiId: multiId, // Use the saved multiId
          );
          _selectedQuestionParts.add(savedQuestionPart);

          debugPrint(
              '[$debugKey] _loadNonRestrictedMultiQuestionData - Added saved questionpart with multiId: $multiId');
        } else {
          debugPrint(
              '[$debugKey] _loadNonRestrictedMultiQuestionData - WARNING: Could not find original questionpart for ID: ${answer.questionpartId}');
        }
      } else {
        debugPrint(
            '[$debugKey] _loadNonRestrictedMultiQuestionData - Skipping answer with missing questionpartId or multiId');
      }
    }

    debugPrint(
        '[$debugKey] _loadNonRestrictedMultiQuestionData - Loaded ${_selectedQuestionParts.length} question parts for non-restrictedMultiQuestion');
  }

  /// Generate questionpartMultiId in "questionpartid-timestamp" format
  String _generateQuestionPartMultiId(num? questionPartId) {
    final questionPartIdStr = '${questionPartId ?? 0}';
    final timestamp = DateTime.now().millisecondsSinceEpoch %
        256; // Keep within uint8 range (0-255)
    return '$questionPartIdStr-$timestamp';
  }

  /// Remove selected item for non-restrictedMultiQuestion
  Future<void> _removeSelectedItem(int index) async {
    debugPrint(
        '[$debugKey] _removeSelectedItem - Starting removal of item at index: $index');

    if (index < 0 || index >= _selectedQuestionParts.length) {
      debugPrint(
          '[$debugKey] _removeSelectedItem - ERROR: Invalid index $index for list of size ${_selectedQuestionParts.length}');
      return;
    }

    final questionPart = _selectedQuestionParts[index];
    debugPrint(
        '[$debugKey] _removeSelectedItem - Removing questionpart with ID: ${questionPart.questionpartId}, multiId: ${questionPart.questionpartMultiId}');

    // Remove QuestionAnswer data for this specific questionPartMultiId (not questionpartId)
    // This ensures we only remove data for THIS specific instance, not all instances of the same questionpartId
    _removeQuestionAnswerDataForQuestionPartMultiId(
        questionPart.questionpartMultiId);

    // DO NOT remove from database question_parts array - that's the original data
    // The original questionParts should never be modified

    setState(() {
      _selectedQuestionParts.removeAt(index);
    });

    debugPrint(
        '[$debugKey] _removeSelectedItem - Item removed. Remaining items: ${_selectedQuestionParts.length}');

    // Save the updated selection to database
    await _saveQuestionPartSelections();

    // Update completion status and log final state
    await _updateCompletionStatus();
    _logCurrentState();
  }

  /// Remove QuestionAnswer data for a specific questionpartId (all instances)
  void _removeQuestionAnswerDataForQuestionPart(int? questionpartId) {
    if (widget.taskId == null ||
        widget.formId == null ||
        questionpartId == null) {
      return;
    }

    try {
      final realm = RealmDatabase.instance.realm;

      // Find the task with the matching taskId
      final taskModel = realm.query<TaskDetailModel>(
          'taskId == \$0', [widget.taskId!.toInt()]).firstOrNull;

      if (taskModel == null) {
        debugPrint('Task not found in database for taskId: ${widget.taskId}');
        return;
      }

      // Find the form with the matching formId
      final formModel = taskModel.forms
          .where((form) => form.formId == widget.formId!.toInt())
          .firstOrNull;

      if (formModel == null) {
        debugPrint('Form not found in task for formId: ${widget.formId}');
        return;
      }

      realm.write(() {
        // Remove all QuestionAnswer records with matching questionpartId
        final answersToRemove = formModel.questionAnswers
            .where((qa) => qa.questionpartId == questionpartId)
            .toList();

        for (final answer in answersToRemove) {
          formModel.questionAnswers.remove(answer);
        }

        debugPrint(
            'Removed ${answersToRemove.length} QuestionAnswer records for questionpartId: $questionpartId');
      });
    } catch (e) {
      debugPrint('Error removing QuestionAnswer data for questionpartId: $e');
    }
  }

  /// Remove QuestionAnswer data for a specific questionPartMultiId (all instances)
  void _removeQuestionAnswerDataForQuestionPartMultiId(
      String? questionPartMultiId) {
    if (widget.taskId == null ||
        widget.formId == null ||
        questionPartMultiId == null) {
      return;
    }

    try {
      final realm = RealmDatabase.instance.realm;

      // Find the task with the matching taskId
      final taskModel = realm.query<TaskDetailModel>(
          'taskId == \$0', [widget.taskId!.toInt()]).firstOrNull;

      if (taskModel == null) {
        debugPrint('Task not found in database for taskId: ${widget.taskId}');
        return;
      }

      // Find the form with the matching formId
      final formModel = taskModel.forms
          .where((form) => form.formId == widget.formId!.toInt())
          .firstOrNull;

      if (formModel == null) {
        debugPrint('Form not found in task for formId: ${widget.formId}');
        return;
      }

      realm.write(() {
        // Remove all QuestionAnswer records with matching questionPartMultiId
        final answersToRemove = formModel.questionAnswers
            .where((qa) => qa.questionPartMultiId == questionPartMultiId)
            .toList();

        for (final answer in answersToRemove) {
          formModel.questionAnswers.remove(answer);
        }

        debugPrint(
            'Removed ${answersToRemove.length} QuestionAnswer records for questionPartMultiId: $questionPartMultiId');
      });
    } catch (e) {
      debugPrint(
          'Error removing QuestionAnswer data for questionPartMultiId: $e');
    }
  }

  /// Add question part to local state only (do NOT modify database question_parts array)
  Future<void> _addQuestionPartToLocalState(
      QuestionPart originalQuestionPart) async {
    debugPrint(
        '[$debugKey] _addQuestionPartToLocalState - Adding questionpart with ID: ${originalQuestionPart.questionpartId}');

    if (widget.taskId == null ||
        widget.formId == null ||
        _question?.questionId == null) {
      debugPrint(
          '[$debugKey] _addQuestionPartToLocalState - ERROR: Missing required IDs');
      return;
    }

    // Generate a unique questionpartMultiId for this instance
    final newMultiId =
        _generateQuestionPartMultiId(originalQuestionPart.questionpartId);

    // Create a new instance with unique multiId
    final newQuestionPart = QuestionPart(
      projectid: originalQuestionPart.projectid,
      questionpartId: originalQuestionPart.questionpartId,
      questionpartDescription: originalQuestionPart.questionpartDescription,
      price: originalQuestionPart.price,
      modifiedTimeStampQuestionpart:
          originalQuestionPart.modifiedTimeStampQuestionpart,
      targetByCycle: originalQuestionPart.targetByCycle,
      targetByGroup: originalQuestionPart.targetByGroup,
      targetByCompany: originalQuestionPart.targetByCompany,
      targetByRegion: originalQuestionPart.targetByRegion,
      targetByBudget: originalQuestionPart.targetByBudget,
      osaForm: originalQuestionPart.osaForm,
      companyId: originalQuestionPart.companyId,
      itemImage: originalQuestionPart.itemImage,
      targeted: originalQuestionPart.targeted,
      questionpartMultiId: newMultiId, // This makes each instance unique
    );

    debugPrint(
        '[$debugKey] _addQuestionPartToLocalState - Generated unique multiId: $newMultiId');

    // DO NOT modify the database question_parts array - that contains the original data
    // Only add to local selected list

    setState(() {
      _selectedQuestionParts.add(newQuestionPart);
    });

    debugPrint(
        '[$debugKey] _addQuestionPartToLocalState - Added to local state. Total items: ${_selectedQuestionParts.length}');

    // Save the updated selection to database (for QuestionAnswer records)
    await _saveQuestionPartSelections();

    // Update completion status and log final state
    await _updateCompletionStatus();
    _logCurrentState();
  }

  /// Change question part in local state
  Future<void> _changeQuestionPartInLocalState(
      int index, QuestionPart newQuestionPart) async {
    debugPrint(
        '[$debugKey] _changeQuestionPartInLocalState - Changing item at index: $index');

    if (index < 0 || index >= _selectedQuestionParts.length) {
      debugPrint(
          '[$debugKey] _changeQuestionPartInLocalState - ERROR: Invalid index $index for list of size ${_selectedQuestionParts.length}');
      return;
    }

    final oldQuestionPart = _selectedQuestionParts[index];
    debugPrint(
        '[$debugKey] _changeQuestionPartInLocalState - Old: ${oldQuestionPart.questionpartId}/${oldQuestionPart.questionpartMultiId}, New: ${newQuestionPart.questionpartId}');

    // Remove old QuestionAnswer data for the specific questionPartMultiId (not questionpartId)
    _removeQuestionAnswerDataForQuestionPartMultiId(
        oldQuestionPart.questionpartMultiId);

    // Generate new unique multiId for the new selection
    final newMultiId =
        _generateQuestionPartMultiId(newQuestionPart.questionpartId);
    debugPrint(
        '[$debugKey] _changeQuestionPartInLocalState - Generated new multiId: $newMultiId');

    setState(() {
      _selectedQuestionParts[index] = QuestionPart(
        projectid: newQuestionPart.projectid,
        questionpartId: newQuestionPart.questionpartId,
        questionpartDescription: newQuestionPart.questionpartDescription,
        price: newQuestionPart.price,
        modifiedTimeStampQuestionpart:
            newQuestionPart.modifiedTimeStampQuestionpart,
        targetByCycle: newQuestionPart.targetByCycle,
        targetByGroup: newQuestionPart.targetByGroup,
        targetByCompany: newQuestionPart.targetByCompany,
        targetByRegion: newQuestionPart.targetByRegion,
        targetByBudget: newQuestionPart.targetByBudget,
        osaForm: newQuestionPart.osaForm,
        companyId: newQuestionPart.companyId,
        itemImage: newQuestionPart.itemImage,
        targeted: newQuestionPart.targeted,
        questionpartMultiId: newMultiId, // New unique multiId
      );
    });

    // Save the updated selection to database (for QuestionAnswer records)
    await _saveQuestionPartSelections();

    // Update completion status and log final state
    await _updateCompletionStatus();
    _logCurrentState();

    debugPrint(
        '[$debugKey] _changeQuestionPartInLocalState - Change completed successfully');
  }

  /// Check completion status using FormUtils
  Future<void> _checkCompletionStatus({bool forceRefresh = false}) async {
    if (_question == null) return;

    final newStatus = <String, bool>{};

    // Combine both lists for checking
    final allPartsToCheck = [
      ..._selectedQuestionParts,
      ..._templateQuestionParts.whereType<QuestionPart>()
    ];

    for (final part in allPartsToCheck) {
      if (part.questionpartId == null || part.questionpartMultiId == null) {
        continue;
      }

      final progress = await FormUtils.getQPMDPageProgress(
        taskId: widget.taskId!,
        formId: widget.formId!,
        questionId: _question!.questionId!,
        questionpartId: part.questionpartId!,
        questionpartMultiId: part.questionpartMultiId!,
      );

      final isCompleted = progress.totalVisible > 0 &&
          progress.totalVisible == progress.totalCompleted;

      newStatus[part.questionpartMultiId!] = isCompleted;
    }

    // Update state if something has changed or if force refresh is requested
    if (forceRefresh || newStatus.toString() != _completionStatus.toString()) {
      setState(() {
        _completionStatus.clear();
        _completionStatus.addAll(newStatus);
      });
      debugPrint(
          '[$debugKey] _checkCompletionStatus - Updated completion status: $newStatus');
    }
  }

  /// Update completion status for all question parts
  Future<void> _updateCompletionStatus({bool forceRefresh = false}) async {
    debugPrint(
        '[$debugKey] _updateCompletionStatus - Checking completion status (forceRefresh: $forceRefresh)');
    // Await completion status check to ensure UI updates properly
    await _checkCompletionStatus(forceRefresh: forceRefresh);
  }

  /// Debug method to log current state
  void _logCurrentState() {
    debugPrint('[$debugKey] _logCurrentState - === CURRENT STATE DEBUG ===');
    debugPrint(
        '[$debugKey] _logCurrentState - Question ID: ${_question?.questionId}');
    debugPrint(
        '[$debugKey] _logCurrentState - Is restricted multi: $_isRestrictedMultiQuestion');
    debugPrint(
        '[$debugKey] _logCurrentState - Available question parts count: ${_availableQuestionParts.length}');

    for (int i = 0; i < _availableQuestionParts.length; i++) {
      final qp = _availableQuestionParts[i];
      debugPrint(
          '[$debugKey] _logCurrentState - Available[$i]: ID=${qp.questionpartId}, desc="${qp.questionpartDescription}"');
    }

    if (_isRestrictedMultiQuestion) {
      debugPrint(
          '[$debugKey] _logCurrentState - Template question parts count: ${_templateQuestionParts.length}');
      for (int i = 0; i < _templateQuestionParts.length; i++) {
        final qp = _templateQuestionParts[i];
        if (qp != null) {
          debugPrint(
              '[$debugKey] _logCurrentState - Template[$i]: ID=${qp.questionpartId}, multiId=${qp.questionpartMultiId}, desc="${qp.questionpartDescription}"');
        } else {
          debugPrint('[$debugKey] _logCurrentState - Template[$i]: null');
        }
      }
    } else {
      debugPrint(
          '[$debugKey] _logCurrentState - Selected question parts count: ${_selectedQuestionParts.length}');
      for (int i = 0; i < _selectedQuestionParts.length; i++) {
        final qp = _selectedQuestionParts[i];
        debugPrint(
            '[$debugKey] _logCurrentState - Selected[$i]: ID=${qp.questionpartId}, multiId=${qp.questionpartMultiId}, desc="${qp.questionpartDescription}"');
      }
    }

    debugPrint(
        '[$debugKey] _logCurrentState - Completion status count: ${_completionStatus.length}');
    _completionStatus.forEach((key, value) {
      debugPrint('[$debugKey] _logCurrentState - Completion[$key]: $value');
    });

    debugPrint('[$debugKey] _logCurrentState - === END STATE DEBUG ===');
  }

  /// Save question part selections to database
  Future<void> _saveQuestionPartSelections() async {
    debugPrint(
        '[$debugKey] _saveQuestionPartSelections - Starting save process');

    if (widget.taskId == null ||
        widget.formId == null ||
        _question?.questionId == null) {
      debugPrint(
          '[$debugKey] _saveQuestionPartSelections - ERROR: Cannot save: missing taskId, formId, or questionId');
      return;
    }

    try {
      final realm = RealmDatabase.instance.realm;

      // Find the task with the matching taskId
      final taskModel = realm.query<TaskDetailModel>(
          'taskId == \$0', [widget.taskId!.toInt()]).firstOrNull;

      if (taskModel == null) {
        debugPrint(
            '[$debugKey] _saveQuestionPartSelections - ERROR: Task not found in database for taskId: ${widget.taskId}');
        return;
      }

      // Find the form with the matching formId
      final formModel = taskModel.forms
          .where((form) => form.formId == widget.formId!.toInt())
          .firstOrNull;

      if (formModel == null) {
        debugPrint(
            '[$debugKey] _saveQuestionPartSelections - ERROR: Form not found in task for formId: ${widget.formId}');
        return;
      }

      realm.write(() {
        // Remove existing selection records for this question (preserve measurement data)
        // Selection records have questionpartId but no measurementId
        // Measurement data records have both questionpartId and measurementId
        final existingSelectionAnswers = formModel.questionAnswers
            .where((qa) =>
                qa.questionId == _question!.questionId!.toInt() &&
                qa.measurementId ==
                    null) // Only remove selection records, not measurement data
            .toList();

        debugPrint(
            '[$debugKey] _saveQuestionPartSelections - Found ${existingSelectionAnswers.length} existing selection records to remove');

        for (final existingAnswer in existingSelectionAnswers) {
          debugPrint(
              '[$debugKey] _saveQuestionPartSelections - Removing selection record: questionpartId=${existingAnswer.questionpartId}, multiId=${existingAnswer.questionPartMultiId}');
          formModel.questionAnswers.remove(existingAnswer);
        }

        debugPrint(
            '[$debugKey] _saveQuestionPartSelections - Removed ${existingSelectionAnswers.length} selection records, preserved measurement data');

        // Add new question answers for selected question parts
        if (_isRestrictedMultiQuestion) {
          debugPrint(
              '[$debugKey] _saveQuestionPartSelections - Saving ${_templateQuestionParts.length} template question parts for restrictedMultiQuestion');

          // For restrictedMultiQuestion, save template selections
          for (int i = 0; i < _templateQuestionParts.length; i++) {
            final questionPart = _templateQuestionParts[i];
            if (questionPart != null) {
              final multiId = questionPart.questionpartMultiId ??
                  _generateQuestionPartMultiId(questionPart.questionpartId);

              // Update the question part in the list with the new multi-ID if it was missing
              if (questionPart.questionpartMultiId == null) {
                debugPrint(
                    '[$debugKey] _saveQuestionPartSelections - Generating new multiId for template item $i: $multiId');
                _templateQuestionParts[i] = QuestionPart(
                  projectid: questionPart.projectid,
                  questionpartId: questionPart.questionpartId,
                  questionpartDescription: questionPart.questionpartDescription,
                  price: questionPart.price,
                  modifiedTimeStampQuestionpart:
                      questionPart.modifiedTimeStampQuestionpart,
                  targetByCycle: questionPart.targetByCycle,
                  targetByGroup: questionPart.targetByGroup,
                  targetByCompany: questionPart.targetByCompany,
                  targetByRegion: questionPart.targetByRegion,
                  targetByBudget: questionPart.targetByBudget,
                  osaForm: questionPart.osaForm,
                  companyId: questionPart.companyId,
                  itemImage: questionPart.itemImage,
                  targeted: questionPart.targeted,
                  questionpartMultiId: multiId,
                );
              }

              final questionAnswer = QuestionAnswerModel(
                taskId: widget.taskId!.toInt(),
                formId: widget.formId!.toInt(),
                questionId: _question!.questionId!.toInt(),
                questionpartId: questionPart.questionpartId?.toInt(),
                flip: _question?.flip,
                questionPartMultiId: multiId,
                isComment: false,
              );
              debugPrint(
                  '[$debugKey] _saveQuestionPartSelections - Adding template selection record: questionpartId=${questionPart.questionpartId}, multiId=$multiId');
              formModel.questionAnswers.add(questionAnswer);
            }
          }
        } else {
          debugPrint(
              '[$debugKey] _saveQuestionPartSelections - Saving ${_selectedQuestionParts.length} selected question parts for non-restrictedMultiQuestion');

          // For non-restrictedMultiQuestion, save selected question parts
          for (int i = 0; i < _selectedQuestionParts.length; i++) {
            final questionPart = _selectedQuestionParts[i];
            final multiId = questionPart.questionpartMultiId ??
                _generateQuestionPartMultiId(questionPart.questionpartId);

            // Update the question part in the list with the new multi-ID if it was missing
            if (questionPart.questionpartMultiId == null) {
              debugPrint(
                  '[$debugKey] _saveQuestionPartSelections - Generating new multiId for selected item $i: $multiId');
              _selectedQuestionParts[i] = QuestionPart(
                projectid: questionPart.projectid,
                questionpartId: questionPart.questionpartId,
                questionpartDescription: questionPart.questionpartDescription,
                price: questionPart.price,
                modifiedTimeStampQuestionpart:
                    questionPart.modifiedTimeStampQuestionpart,
                targetByCycle: questionPart.targetByCycle,
                targetByGroup: questionPart.targetByGroup,
                targetByCompany: questionPart.targetByCompany,
                targetByRegion: questionPart.targetByRegion,
                targetByBudget: questionPart.targetByBudget,
                osaForm: questionPart.osaForm,
                companyId: questionPart.companyId,
                itemImage: questionPart.itemImage,
                targeted: questionPart.targeted,
                questionpartMultiId: multiId,
              );
            }
            final questionAnswer = QuestionAnswerModel(
              taskId: widget.taskId!.toInt(),
              formId: widget.formId!.toInt(),
              questionId: _question!.questionId!.toInt(),
              questionpartId: questionPart.questionpartId?.toInt(),
              flip: _question?.flip,
              questionPartMultiId: multiId,
              isComment: false,
            );
            debugPrint(
                '[$debugKey] _saveQuestionPartSelections - Adding selected record: questionpartId=${questionPart.questionpartId}, multiId=$multiId');
            formModel.questionAnswers.add(questionAnswer);
          }
        }
      });

      debugPrint(
          '[$debugKey] _saveQuestionPartSelections - Successfully saved question part selections to database');
    } catch (e) {
      debugPrint(
          '[$debugKey] _saveQuestionPartSelections - ERROR: Error saving question part selections: $e');
    }
  }

  /// Show template item selection bottom sheet for restrictedMultiQuestion
  void _showTemplateItemBottomSheet(int templateIndex) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      useRootNavigator: true,
      builder: (context) {
        // Calculate dynamic height based on content
        const itemHeight = 56.0;
        const headerHeight = 80.0;
        const bottomPadding = 16.0;
        final maxContentHeight = MediaQuery.of(context).size.height * 0.7;
        final calculatedHeight = headerHeight +
            (_originalQuestionPartsForSelection.length * itemHeight) +
            bottomPadding;
        final finalHeight = calculatedHeight > maxContentHeight
            ? maxContentHeight
            : calculatedHeight;

        return Container(
          height: finalHeight,
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            children: [
              // Handle bar
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.only(top: 12),
                decoration: BoxDecoration(
                  color: AppColors.blackTint2,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // Header
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  'Select item',
                  style: Theme.of(context).textTheme.montserratheadingmedium,
                ),
              ),

              // Items list
              Expanded(
                child: ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  itemCount: _originalQuestionPartsForSelection.length,
                  itemBuilder: (context, index) {
                    final questionPart =
                        _originalQuestionPartsForSelection[index];
                    final currentSelection =
                        _templateQuestionParts[templateIndex];
                    final isCurrentSelection =
                        currentSelection?.questionpartId ==
                            questionPart.questionpartId;

                    return InkWell(
                      onTap: () async {
                        // If there's an existing selection, remove its measurement data
                        if (currentSelection != null) {
                          _removeQuestionAnswerDataForQuestionPartMultiId(
                              currentSelection.questionpartMultiId);
                        }

                        final newMultiId = _generateQuestionPartMultiId(
                            questionPart.questionpartId);

                        setState(() {
                          _templateQuestionParts[templateIndex] = QuestionPart(
                            projectid: questionPart.projectid,
                            questionpartId: questionPart.questionpartId,
                            questionpartDescription:
                                questionPart.questionpartDescription,
                            price: questionPart.price,
                            modifiedTimeStampQuestionpart:
                                questionPart.modifiedTimeStampQuestionpart,
                            targetByCycle: questionPart.targetByCycle,
                            targetByGroup: questionPart.targetByGroup,
                            targetByCompany: questionPart.targetByCompany,
                            targetByRegion: questionPart.targetByRegion,
                            targetByBudget: questionPart.targetByBudget,
                            osaForm: questionPart.osaForm,
                            companyId: questionPart.companyId,
                            itemImage: questionPart.itemImage,
                            targeted: questionPart.targeted,
                            questionpartMultiId:
                                newMultiId, // Assign new multi-ID
                          );
                        });
                        context.router.maybePop();
                        // Save the updated selection to database
                        await _saveQuestionPartSelections();
                        await _updateCompletionStatus();
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 12.0),
                        decoration: const BoxDecoration(
                          border: Border(
                            bottom: BorderSide(
                              color: AppColors.blackTint2,
                              width: 0.5,
                            ),
                          ),
                        ),
                        child: Row(
                          children: [
                            Expanded(
                              child: Text(
                                questionPart.questionpartDescription ??
                                    'Unnamed Item',
                                style: Theme.of(context)
                                    .textTheme
                                    .montserratFormsField
                                    .copyWith(
                                      color: AppColors.black,
                                    ),
                              ),
                            ),
                            if (isCurrentSelection)
                              const Icon(
                                Icons.radio_button_checked,
                                color: AppColors.primaryBlue,
                                size: 20,
                              ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    // Update completion status when returning to this page (only if not loading)
    if (!_isLoading && _question != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        // Force refresh completion status when returning from navigation
        _updateCompletionStatus(forceRefresh: true);
      });
    }

    return Scaffold(
      backgroundColor: AppColors.lightGrey2,
      appBar: CustomAppBar(
        title: _question?.questionDescription ?? 'Store details',
      ),
      body: _isLoading
          ? _buildLoadingState()
          : _errorMessage != null
              ? _buildErrorState(textTheme)
              : _availableQuestionParts.isEmpty
                  ? const EmptyState(message: 'No Question Parts Available')
                  : SingleChildScrollView(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (_isRestrictedMultiQuestion) ...[
                            // RestrictedMultiQuestion: Show template dropdowns
                            ..._buildRestrictedMultiQuestionWidgets(textTheme),
                          ] else ...[
                            // Non-restrictedMultiQuestion: Show add dropdown and selected items
                            _buildMainDropdown(textTheme),
                            const Gap(16),

                            // Selected items list
                            if (_selectedQuestionParts.isNotEmpty) ...[
                              ..._selectedQuestionParts
                                  .asMap()
                                  .entries
                                  .map((entry) {
                                final index = entry.key;
                                final questionPart = entry.value;
                                return Padding(
                                  padding: const EdgeInsets.only(bottom: 16),
                                  child: _buildSelectedItemCard(
                                      questionPart, index, textTheme),
                                );
                              }),
                            ],
                          ],
                        ],
                      ),
                    ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: CircularProgressIndicator(),
    );
  }

  Widget _buildErrorState(TextTheme textTheme) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: AppColors.blackTint1,
            ),
            const Gap(16),
            Text(
              'Error Loading Question',
              style: textTheme.montserratheadingmedium.copyWith(
                color: AppColors.blackTint1,
              ),
            ),
            const Gap(8),
            Text(
              _errorMessage ?? 'An unknown error occurred',
              style: textTheme.montserratParagraphSmall.copyWith(
                color: AppColors.blackTint1,
              ),
              textAlign: TextAlign.center,
            ),
            const Gap(16),
            ElevatedButton(
              onPressed: () {
                setState(() {
                  _isLoading = true;
                  _errorMessage = null;
                });
                _loadQuestionFromDatabase();
              },
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  /// Build widgets for restrictedMultiQuestion
  List<Widget> _buildRestrictedMultiQuestionWidgets(TextTheme textTheme) {
    final widgets = <Widget>[];

    for (int i = 0; i < _templateQuestionParts.length; i++) {
      widgets.add(
        Padding(
          padding: const EdgeInsets.only(bottom: 16.0),
          child: _buildTemplateDropdown(i, textTheme),
        ),
      );
    }

    return widgets;
  }

  /// Build a template dropdown for restrictedMultiQuestion
  Widget _buildTemplateDropdown(int index, TextTheme textTheme) {
    final selectedQuestionPart = _templateQuestionParts[index];

    // Get completion status for this question part
    final questionPartMultiId = selectedQuestionPart?.questionpartMultiId ?? '';
    final isCompleted = _completionStatus[questionPartMultiId] ?? false;

    return Row(
      children: [
        // Dropdown container
        Expanded(
          child: GestureDetector(
            onTap: selectedQuestionPart != null
                ? () async {
                    // Navigate to QPMDPage with the selected question part
                    await context.router.push(QPMDRoute(
                      questionId: _question?.questionId,
                      questionpartId: selectedQuestionPart.questionpartId,
                      taskId: widget.taskId,
                      formId: widget.formId,
                      questionpartMultiId: questionPartMultiId,
                    ));
                    // Refresh completion status after returning
                    await _updateCompletionStatus(forceRefresh: true);
                  }
                : null,
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16.0),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(10),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.black10,
                    blurRadius: 2,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: Row(
                children: [
                  // Dropdown content
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Progress indicator for template dropdowns
                        Text(
                          '${index + 1}/${_templateQuestionParts.length}',
                          style: textTheme.montserratParagraphXsmall.copyWith(
                            color: AppColors.blackTint1,
                          ),
                        ),
                        const Gap(4),
                        Text(
                          selectedQuestionPart?.questionpartDescription ??
                              'Please select...',
                          style: textTheme.montserratFormsField.copyWith(
                            color: selectedQuestionPart != null
                                ? AppColors.black
                                : AppColors.blackTint1,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Dropdown arrow
                  GestureDetector(
                    onTap: () => _showTemplateItemBottomSheet(index),
                    child: Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        color: AppColors.loginGreen,
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: const Icon(
                        Icons.keyboard_arrow_down,
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),

        // Completion status indicator positioned outside the dropdown (only show if item is selected)
        const Gap(12),
        Container(
          width: 24,
          height: 24,
          decoration: BoxDecoration(
            color: isCompleted ? AppColors.primaryBlue : Colors.grey,
            shape: BoxShape.circle,
          ),
          child: const Icon(
            Icons.check,
            color: Colors.white,
            size: 14,
          ),
        ),
      ],
    );
  }

  Widget _buildMainDropdown(TextTheme textTheme) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: AppColors.black10,
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Row(
        children: [
          // Add button (+)
          GestureDetector(
            onTap: _showAddItemBottomSheet,
            child: Container(
              width: 32,
              height: 32,
              decoration: const BoxDecoration(
                color: AppColors.loginGreen,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.add,
                color: Colors.white,
                size: 20,
              ),
            ),
          ),
          const Gap(12),

          // Dropdown content
          Expanded(
            child: Text(
              'Please select...',
              style: textTheme.montserratFormsField.copyWith(
                color: AppColors.blackTint1,
              ),
            ),
          ),

          // Dropdown arrow
          GestureDetector(
            onTap: _showAddItemBottomSheet,
            child: Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: AppColors.loginGreen,
                borderRadius: BorderRadius.circular(10),
              ),
              child: const Icon(
                Icons.keyboard_arrow_down,
                color: Colors.white,
                size: 20,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSelectedItemCard(
      QuestionPart questionPart, int index, TextTheme textTheme) {
    final hasImage = questionPart.itemImage != null &&
        questionPart.itemImage != '0' &&
        questionPart.itemImage!.isNotEmpty;

    // Get completion status for this question part
    final questionPartMultiId = questionPart.questionpartMultiId ?? '';
    final isCompleted = _completionStatus[questionPartMultiId] ?? false;

    return Row(
      children: [
        // Remove button (-)
        GestureDetector(
          onTap: () async => await _removeSelectedItem(index),
          behavior: HitTestBehavior.opaque,
          child: Container(
            width: 32,
            height: 32,
            decoration: const BoxDecoration(
              color: Colors.red,
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.remove,
              color: Colors.white,
              size: 20,
            ),
          ),
        ),
        const Gap(12),

        // Content area
        Expanded(
          child: GestureDetector(
            onTap: () async {
              // Navigate to QPMDPage with the current question
              await context.router.push(QPMDRoute(
                questionId: _question?.questionId,
                questionpartId: questionPart.questionpartId,
                taskId: widget.taskId,
                formId: widget.formId,
                questionpartMultiId: questionPartMultiId,
              ));
              // Refresh completion status after returning
              await _updateCompletionStatus(forceRefresh: true);
            },
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16.0),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(10),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.black10,
                    blurRadius: 2,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: Row(
                children: [
                  // Content area
                  Expanded(
                    child: Row(
                      children: [
                        // Text content
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Progress indicator (like in the image)
                              Text(
                                '${index + 1}/${_selectedQuestionParts.length}',
                                style: textTheme.montserratParagraphXsmall
                                    .copyWith(
                                  color: AppColors.blackTint1,
                                ),
                              ),
                              const Gap(4),
                              Text(
                                questionPart.questionpartDescription ??
                                    'Unnamed Item',
                                style: textTheme.montserratFormsField,
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          ),
                        ),

                        // Image thumbnail (if available)
                        if (hasImage) ...[
                          const Gap(12),
                          Container(
                            width: 60,
                            height: 60,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(10),
                              color: AppColors.lightGrey1,
                              border: Border.all(color: AppColors.blackTint2),
                            ),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(10),
                              child: Image.network(
                                questionPart.itemImage!,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  return Container(
                                    color: AppColors.lightGrey1,
                                    child: const Icon(
                                      Icons.image,
                                      color: AppColors.blackTint1,
                                      size: 24,
                                    ),
                                  );
                                },
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),

                  const Gap(12),

                  // Change selection dropdown arrow
                  GestureDetector(
                    onTap: () => _showChangeItemBottomSheet(index),
                    behavior: HitTestBehavior.opaque,
                    child: Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        color: AppColors.loginGreen,
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: const Icon(
                        Icons.keyboard_arrow_down,
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),

        // Completion status indicator positioned outside the card
        const Gap(12),
        Container(
          width: 24,
          height: 24,
          decoration: BoxDecoration(
            color: isCompleted ? AppColors.primaryBlue : Colors.grey,
            shape: BoxShape.circle,
          ),
          child: const Icon(
            Icons.check,
            color: Colors.white,
            size: 14,
          ),
        ),
      ],
    );
  }

  void _showAddItemBottomSheet() {
    debugPrint(
        '[$debugKey] _showAddItemBottomSheet - Showing add item bottom sheet with [38;5;2m${_availableQuestionParts.length}[0m available items');
    _logCurrentState();

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      useRootNavigator: true,
      builder: (context) {
        // Calculate dynamic height based on content
        const itemHeight = 56.0;
        const headerHeight = 80.0;
        const bottomPadding = 16.0;
        final maxContentHeight = MediaQuery.of(context).size.height * 0.7;
        final calculatedHeight = headerHeight +
            (_originalQuestionPartsForSelection.length * itemHeight) +
            bottomPadding;
        final finalHeight = calculatedHeight > maxContentHeight
            ? maxContentHeight
            : calculatedHeight;

        return Container(
          height: finalHeight,
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            children: [
              // Handle bar
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.only(top: 12),
                decoration: BoxDecoration(
                  color: AppColors.blackTint2,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // Header
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  'Select item',
                  style: Theme.of(context).textTheme.montserratheadingmedium,
                ),
              ),

              // Items list
              Expanded(
                child: ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  itemCount: _originalQuestionPartsForSelection.length,
                  itemBuilder: (context, index) {
                    final questionPart =
                        _originalQuestionPartsForSelection[index];
                    // Count how many times this part is already selected
                    final count = _selectedQuestionParts
                        .where((p) =>
                            p.questionpartId == questionPart.questionpartId)
                        .length;

                    return InkWell(
                      onTap: () async {
                        await _addQuestionPartToLocalState(questionPart);
                        if (context.mounted) {
                          context.router.maybePop();
                        }
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 12.0),
                        decoration: const BoxDecoration(
                          border: Border(
                            bottom: BorderSide(
                              color: AppColors.blackTint2,
                              width: 0.5,
                            ),
                          ),
                        ),
                        child: Row(
                          children: [
                            Expanded(
                              child: Text(
                                questionPart.questionpartDescription ??
                                    'Unnamed Item',
                                style: Theme.of(context)
                                    .textTheme
                                    .montserratFormsField
                                    .copyWith(
                                      color: AppColors.black,
                                    ),
                              ),
                            ),
                            if (count > 0)
                              Padding(
                                padding: const EdgeInsets.only(left: 8.0),
                                child: Text('x$count',
                                    style: const TextStyle(
                                      color: AppColors.primaryBlue,
                                      fontWeight: FontWeight.bold,
                                    )),
                              ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void _showChangeItemBottomSheet(int index) {
    debugPrint(
        '[$debugKey] _showChangeItemBottomSheet - Showing change item bottom sheet for index $index');

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      useRootNavigator: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        // Calculate dynamic height based on content
        const itemHeight = 56.0;
        const headerHeight = 80.0;
        const bottomPadding = 16.0;
        final maxContentHeight = MediaQuery.of(context).size.height * 0.7;
        final calculatedHeight = headerHeight +
            (_originalQuestionPartsForSelection.length * itemHeight) +
            bottomPadding;
        final finalHeight = calculatedHeight > maxContentHeight
            ? maxContentHeight
            : calculatedHeight;

        return Container(
          height: finalHeight,
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            children: [
              // Handle bar
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.only(top: 12),
                decoration: BoxDecoration(
                  color: AppColors.blackTint2,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // Header
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  'Change Selection',
                  style: Theme.of(context).textTheme.montserratheadingmedium,
                ),
              ),

              // Items list
              Expanded(
                child: ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  itemCount: _originalQuestionPartsForSelection.length,
                  itemBuilder: (context, itemIndex) {
                    final questionPart =
                        _originalQuestionPartsForSelection[itemIndex];
                    final currentItem = _selectedQuestionParts[index];
                    final isCurrentSelection = currentItem.questionpartId ==
                        questionPart.questionpartId;
                    final isAlreadySelected = _selectedQuestionParts.any(
                        (item) =>
                            item.questionpartId == questionPart.questionpartId);

                    return InkWell(
                      onTap: isCurrentSelection
                          ? null
                          : () async {
                              // Change question part in local state
                              await _changeQuestionPartInLocalState(
                                  index, questionPart);
                              if (context.mounted) {
                                context.router.maybePop();
                              }
                            },
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 12.0),
                        decoration: const BoxDecoration(
                          border: Border(
                            bottom: BorderSide(
                              color: AppColors.blackTint2,
                              width: 0.5,
                            ),
                          ),
                        ),
                        child: Row(
                          children: [
                            Expanded(
                              child: Text(
                                questionPart.questionpartDescription ??
                                    'Unnamed Item',
                                style: Theme.of(context)
                                    .textTheme
                                    .montserratFormsField
                                    .copyWith(
                                      color: (isAlreadySelected &&
                                              !isCurrentSelection)
                                          ? AppColors.blackTint1
                                          : AppColors.black,
                                    ),
                              ),
                            ),
                            if (isCurrentSelection)
                              const Icon(
                                Icons.radio_button_checked,
                                color: AppColors.primaryBlue,
                                size: 20,
                              )
                            else if (isAlreadySelected)
                              const Icon(
                                Icons.check,
                                color: AppColors.loginGreen,
                                size: 20,
                              ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
