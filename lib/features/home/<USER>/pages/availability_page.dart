import 'package:flutter/material.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/config/themes/app_fonts.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/availability/availability_cubit.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/core/utils/snackbar_service.dart';
import 'package:storetrack_app/features/home/<USER>/services/sync_service.dart';
import '../../domain/entities/availability_response_entity.dart';

@RoutePage()
class AvailabilityPage extends StatefulWidget {
  const AvailabilityPage({super.key});

  @override
  State<AvailabilityPage> createState() => _AvailabilityPageState();
}

class _AvailabilityPageState extends State<AvailabilityPage> {
  final Map<String, List<Map<String, String>>> dayTimeSlots = {};
  final Map<String, bool> dayAvailabilityStatus = {};
  bool _isInitialized = false;

  void _initializeFromApiData(List<DayAvailabilityEntity> days) {
    if (_isInitialized) return;

    for (final dayEntity in days) {
      final day = dayEntity.dayDescription;

      // Check if day has valid (non-zero) time spans
      final validSpans = dayEntity.daySpans
          .where(
              (span) => !(span.startHour == '0:00' && span.endHour == '0:00'))
          .toList();

      final hasValidSpans = validSpans.isNotEmpty;
      dayAvailabilityStatus[day] = hasValidSpans;

      if (hasValidSpans) {
        // Use only valid spans from API
        dayTimeSlots[day] = validSpans
            .map((span) => {
                  'start': span.startHour,
                  'end': span.endHour,
                })
            .toList();
      } else {
        // For unavailable days, initialize with empty list
        dayTimeSlots[day] = [];
      }
    }

    _isInitialized = true;
  }

  Future<String?> _showCustomTimePicker(BuildContext context,
      {String? currentTime}) async {
    // Parse current time to set initial values
    int selectedHour = 8; // Default to 8 AM
    int selectedMinute = 0;

    if (currentTime != null && currentTime.isNotEmpty) {
      try {
        final parts = currentTime.split(':');
        if (parts.length == 2) {
          selectedHour = int.parse(parts[0]);
          selectedMinute = int.parse(parts[1]);
          // Round minutes to nearest 30
          selectedMinute = selectedMinute >= 30 ? 30 : 0;
        }
      } catch (e) {
        // Use defaults if parsing fails
      }
    }

    // Create controllers to set initial scroll positions
    final hourController =
        FixedExtentScrollController(initialItem: selectedHour);
    final minuteController = FixedExtentScrollController(
        initialItem: selectedMinute == 30 ? 1 : 0); // 0 for :00, 1 for :30

    return showDialog<String>(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return WillPopScope(
              onWillPop: () async {
                hourController.dispose();
                minuteController.dispose();
                return true;
              },
              child: AlertDialog(
                backgroundColor: Colors.white,
                elevation: 24,
                surfaceTintColor: Colors.transparent,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
                titlePadding: EdgeInsets.zero,
                contentPadding: const EdgeInsets.fromLTRB(20, 16, 20, 0),
                actionsPadding: const EdgeInsets.fromLTRB(16, 0, 16, 12),
                title: Container(
                  decoration: const BoxDecoration(
                    color: AppColors.primaryBlue,
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(20),
                      topRight: Radius.circular(20),
                    ),
                  ),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 16,
                  ),
                  child: const Text(
                    'Select Time',
                    style: TextStyle(
                      color: Colors.white,
                      fontFamily: AppFonts.montserrat,
                      fontWeight: FontWeight.w700,
                      fontSize: 18,
                    ),
                  ),
                ),
                content: SizedBox(
                  height: 200,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Hours
                      SizedBox(
                        width: 70,
                        child: ListWheelScrollView(
                          controller: hourController,
                          itemExtent: 40,
                          useMagnifier: true,
                          magnification: 1.5,
                          physics: const FixedExtentScrollPhysics(),
                          onSelectedItemChanged: (index) {
                            setState(() {
                              selectedHour = index;
                            });
                          },
                          children: List.generate(24, (index) {
                            return Center(
                              child: Text(
                                index.toString().padLeft(2, '0'),
                                style: TextStyle(
                                  fontSize: 20,
                                  fontFamily: AppFonts.montserrat,
                                  color: selectedHour == index
                                      ? AppColors.primaryBlue
                                      : AppColors.blackTint1,
                                  fontWeight: selectedHour == index
                                      ? FontWeight.bold
                                      : FontWeight.normal,
                                ),
                              ),
                            );
                          }),
                        ),
                      ),
                      const Text(
                        ':',
                        style: TextStyle(
                          fontSize: 20,
                          fontFamily: AppFonts.montserrat,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      // Minutes (only show 00 and 30 for hourly slots)
                      SizedBox(
                        width: 70,
                        child: ListWheelScrollView(
                          controller: minuteController,
                          itemExtent: 40,
                          useMagnifier: true,
                          magnification: 1.5,
                          physics: const FixedExtentScrollPhysics(),
                          onSelectedItemChanged: (index) {
                            setState(() {
                              selectedMinute = index * 30; // 0 or 30 minutes
                            });
                          },
                          children: [0, 30]
                              .map((minute) => Center(
                                    child: Text(
                                      minute.toString().padLeft(2, '0'),
                                      style: TextStyle(
                                        fontSize: 20,
                                        fontFamily: AppFonts.montserrat,
                                        color: selectedMinute == minute
                                            ? AppColors.primaryBlue
                                            : AppColors.blackTint1,
                                        fontWeight: selectedMinute == minute
                                            ? FontWeight.bold
                                            : FontWeight.normal,
                                      ),
                                    ),
                                  ))
                              .toList(),
                        ),
                      ),
                    ],
                  ),
                ),
                actions: [
                  TextButton(
                    style: TextButton.styleFrom(
                      foregroundColor: AppColors.blackTint1,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 24,
                        vertical: 12,
                      ),
                      textStyle: const TextStyle(
                        fontFamily: AppFonts.montserrat,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    onPressed: () {
                      context.router.maybePop();
                    },
                    child: const Text('Cancel'),
                  ),
                  ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primaryBlue,
                      foregroundColor: Colors.white,
                      elevation: 4,
                      shadowColor: AppColors.primaryBlue,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10),
                      ),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 24,
                        vertical: 12,
                      ),
                      textStyle: const TextStyle(
                        fontFamily: AppFonts.montserrat,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    onPressed: () {
                      final formattedTime =
                          '${selectedHour.toString().padLeft(2, '0')}:${selectedMinute.toString().padLeft(2, '0')}';
                      Navigator.pop(context, formattedTime);
                    },
                    child: const Text('OK'),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  Future<void> _selectTime(
      BuildContext context, String day, int slotIndex, bool isStartTime) async {
    final currentTime =
        dayTimeSlots[day]?[slotIndex][isStartTime ? 'start' : 'end'];
    final String? selectedTime =
        await _showCustomTimePicker(context, currentTime: currentTime);
    if (selectedTime != null) {
      setState(() {
        dayTimeSlots[day]![slotIndex][isStartTime ? 'start' : 'end'] =
            selectedTime;
      });
    }
  }

  Widget _buildTimeSection(String day, int slotIndex) {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          Expanded(
            child: GestureDetector(
              onTap: () => _selectTime(context, day, slotIndex, true),
              child: Container(
                color: Colors.white,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Start Time',
                      style:
                          const TextTheme().montserratTitleExtraSmall.copyWith(
                                color: AppColors.black,
                              ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      dayTimeSlots[day]?[slotIndex]['start'] ?? '08:00',
                      style:
                          const TextTheme().montserratTitleExtraSmall.copyWith(
                                color: AppColors.black,
                              ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          Container(
            width: 1,
            height: 60,
            color: AppColors.borderColor,
          ),
          Expanded(
            child: GestureDetector(
              onTap: () => _selectTime(context, day, slotIndex, false),
              child: Container(
                color: Colors.white,
                child: Padding(
                  padding: const EdgeInsets.only(left: 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'End Time',
                        style: const TextTheme()
                            .montserratTitleExtraSmall
                            .copyWith(
                              color: AppColors.black,
                            ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        dayTimeSlots[day]?[slotIndex]['end'] ?? '17:00',
                        style: const TextTheme()
                            .montserratTitleExtraSmall
                            .copyWith(
                              color: AppColors.black,
                            ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          )
        ],
      ),
    );
  }

  Widget _buildDaySection(
      DayAvailabilityEntity dayEntity, void Function(bool) onChanged) {
    final day = dayEntity.dayDescription;

    // Ensure data is available for this day (fallback if not initialized)
    if (!dayTimeSlots.containsKey(day)) {
      dayTimeSlots[day] = [];
    }

    // Ensure availability status is set (fallback if not initialized)
    if (!dayAvailabilityStatus.containsKey(day)) {
      dayAvailabilityStatus[day] = false;
    }

    return Column(
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                day,
                style: const TextTheme().montserratTitleSmall.copyWith(
                      color: AppColors.primaryBlue,
                    ),
              ),
              const Gap(12),
              if (dayAvailabilityStatus[day] == true &&
                  dayTimeSlots[day]!.length < 3)
                GestureDetector(
                  onTap: () {
                    setState(() {
                      // Add new time slot with default times
                      dayTimeSlots[day]!
                          .add({'start': '08:00', 'end': '17:00'});
                    });
                  },
                  child: const Text(
                    '+',
                    style: TextStyle(
                      fontSize: 24,
                      color: AppColors.black,
                    ),
                  ),
                ),
              Switch(
                value: dayAvailabilityStatus[day] ?? false,
                onChanged: (value) {
                  setState(() {
                    dayAvailabilityStatus[day] = value;

                    if (value) {
                      // When toggle is ON: Add default time span if empty
                      if (dayTimeSlots[day]!.isEmpty) {
                        dayTimeSlots[day] = [
                          {'start': '08:00', 'end': '17:00'}
                        ];
                      }
                    } else {
                      // When toggle is OFF: Clear all time spans
                      dayTimeSlots[day] = [];
                    }
                  });
                  onChanged(value);
                },
                activeColor: AppColors.primaryBlue,
              ),
            ],
          ),
        ),
        Container(
          height: 1,
          color: AppColors.borderColor,
        ),
        // Only show time sections if the day is available
        if (dayAvailabilityStatus[day] == true)
          ...List.generate(
            dayTimeSlots[day]!.length,
            (index) => Column(
              children: [
                _buildTimeSection(day, index),
                Container(
                  height: 1,
                  color: AppColors.borderColor,
                ),
              ],
            ),
          ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => sl<AvailabilityCubit>()..fetchAvailability(isSync: true),
      child: BlocConsumer<AvailabilityCubit, AvailabilityState>(
        listener: (context, state) {
          if (state is AvailabilityError) {
            SnackBarService.error(
              context: context,
              message: state.message,
            );
          } else if (state is AvailabilitySaved) {
            SnackBarService.success(
              context: context,
              message: 'Availability saved successfully',
            );

            // Trigger sync after successful save
            SyncService()
                .sync(context: mounted ? context : null)
                .catchError((error) {
              if (mounted && context.mounted) {
                SnackBarService.error(
                  context: context,
                  message: 'Sync failed: ${error.toString()}',
                );
              }
            });
          }
        },
        builder: (context, state) {
          return Scaffold(
            backgroundColor: Colors.white,
            appBar: CustomAppBar(
              title: 'Availability',
              onBackPressed: () => context.router.maybePop(),
              actions:
                  state is AvailabilityLoaded || state is AvailabilitySaving
                      ? [
                          IconButton(
                            onPressed: state is AvailabilitySaving
                                ? null
                                : () async {
                                    final validationError = await context
                                        .read<AvailabilityCubit>()
                                        .saveAvailability(
                                          dayTimeSlots,
                                          dayAvailabilityStatus,
                                        );

                                    // Show snackbar for validation errors only
                                    if (mounted && validationError != null) {
                                      if (context.mounted) {
                                        SnackBarService.error(
                                          context: context,
                                          message: validationError,
                                        );
                                      }
                                    }
                                  },
                            icon: state is AvailabilitySaving
                                ? const SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      color: Colors.white,
                                    ),
                                  )
                                : const Icon(Icons.check),
                          ),
                        ]
                      : null,
            ),
            body: _buildBody(state, context),
          );
        },
      ),
    );
  }

  Widget _buildBody(AvailabilityState state, BuildContext context) {
    if (state is AvailabilityLoading) {
      return const Center(
        child: CircularProgressIndicator(
          color: AppColors.primaryBlue,
        ),
      );
    } else if (state is AvailabilityLoaded || state is AvailabilitySaving) {
      final days = state is AvailabilityLoaded
          ? state.days
          : (state as AvailabilitySaving).days;

      // Initialize data from API when first loaded
      _initializeFromApiData(days);

      return SingleChildScrollView(
        child: Column(
          children: [
            for (final dayEntity in days)
              _buildDaySection(
                dayEntity,
                (value) {
                  // Update logic is handled within _buildDaySection
                },
              ),
            const SizedBox(height: 50),
          ],
        ),
      );
    } else if (state is AvailabilityError) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 64,
                color: AppColors.blackTint1,
              ),
              const SizedBox(height: 16),
              Text(
                'Error',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontFamily: AppFonts.montserrat,
                      fontWeight: FontWeight.bold,
                    ),
              ),
              const SizedBox(height: 8),
              Text(
                state.message,
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontFamily: AppFonts.montserrat,
                  color: AppColors.blackTint1,
                ),
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () {
                  context
                      .read<AvailabilityCubit>()
                      .fetchAvailability(isSync: true);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primaryBlue,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }
    return const SizedBox.shrink();
  }
}
