import 'dart:io';

import 'package:dio/dio.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:realm/realm.dart';
import 'package:storetrack_app/core/constants/app_constants.dart';
import 'package:storetrack_app/core/database/realm_database.dart';
import 'package:storetrack_app/core/network/api_client.dart';
import 'package:storetrack_app/core/storage/data_manager.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/core/utils/form_utils.dart';
import 'package:storetrack_app/core/utils/photo_utils.dart';
import 'package:storetrack_app/core/utils/signature_utils.dart';
import 'package:storetrack_app/core/utils/image_storage_utils.dart';
import 'package:storetrack_app/features/home/<USER>/mappers/task_detail_mapper.dart';
import 'package:storetrack_app/features/home/<USER>/models/profile_model.dart';
import 'package:storetrack_app/features/home/<USER>/models/task_detail_model.dart';
import 'package:storetrack_app/features/home/<USER>/entities/refresh_forms_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/upload_photo_request_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/upload_photo_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/upload_sign_request_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/upload_signature_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/sync_pic_request_entity.dart'
    as sync_pic;
import 'package:storetrack_app/features/home/<USER>/entities/sync_sign_request_entity.dart'
    as sync_sign;
import 'package:storetrack_app/features/home/<USER>/entities/submit_report_request_entity.dart'
    as submit_report;
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_request_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/photo_type_list_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/photo_type_delete_list_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/signature_type_list_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/signature_type_delete_list_entity.dart';
import 'package:storetrack_app/shared/models/downloaded_file_model.dart';
import 'package:storetrack_app/di/service_locator.dart';

/// Utility class for sync-related operations
///
/// This class provides helper methods for creating API request entities
/// and processing data transformations needed for the four new sync endpoints:
///
/// 1. `/send_task_pic_v4_11` - Upload individual photos
/// 2. `/sync_pic_info_mpt` - Sync photo metadata after all uploads
/// 3. `/send_task_sig_v4_11` - Upload individual signatures
/// 4. `/sync_sig_info` - Sync signature metadata after all uploads
///
/// Usage example:
/// ```dart
/// // 1. Upload individual photos first
/// final photosToUpload = SyncUtils.getPhotosToUpload(tasks);
/// for (final photoData in photosToUpload) {
///   final request = await SyncUtils.createUploadPhotoRequest(
///     photo: photoData['photo'],
///     taskId: photoData['taskId'],
///     folderId: photoData['folderId'],
///   );
///   final result = await homeRepository.uploadPhoto(request);
/// }
///
/// // 2. Then sync photo metadata
/// final syncRequest = await SyncUtils.createSyncPicInfoRequest(tasks: tasks);
/// final syncResult = await homeRepository.syncPhotoInfo(syncRequest);
/// ```
class SyncUtils {
  /// Get device UID from DataManager
  static Future<String> getDeviceUid() async {
    final dataManager = sl<DataManager>();
    return await dataManager.getOrCreateDeviceId();
  }

  /// Create upload photo request entity from photo data
  static Future<UploadPhotoRequestEntity> createUploadPhotoRequest({
    required Photo photo,
    required int taskId,
    required int folderId,
  }) async {
    final dataManager = sl<DataManager>();

    final request = UploadPhotoRequestEntity();
    request.token = await dataManager.getAuthToken();
    request.userId = int.parse(await dataManager.getUserId() ?? "0");
    request.taskId = taskId;
    request.folderId = folderId;
    request.photoId = photo.photoId?.toInt();
    request.photoDate = null;
    request.photoCaption = photo.caption;
    request.cannotUploadMandatory = photo.cannotUploadMandatory;
    request.formId = photo.formId?.toInt();
    request.questionId = photo.questionId?.toInt();
    request.measurementId = photo.measurementId?.toInt();
    request.questionpartId = photo.questionpartId?.toInt();
    request.questionPartMultiId = photo.questionPartMultiId;
    request.measurementPhototypeId = photo.measurementPhototypeId?.toInt();
    request.deviceuid = await dataManager.getOrCreateDeviceId();

    if (photo.cannotUploadMandatory == true) {
      request.pictureBlob = "-1";
    } else {
      // Use localPath if available, otherwise use photoUrl
      final imagePath = photo.localPath ?? photo.photoUrl ?? "";
      request.pictureBlob =
          await ImageStorageUtils.encodeFileToBase64(imagePath);
    }

    return request;
  }

  /// Create upload signature request entity from signature data
  static Future<UploadSignRequestEntity> createUploadSignatureRequest({
    required Signature signature,
    required int taskId,
    required int folderId,
  }) async {
    final dataManager = sl<DataManager>();

    final request = UploadSignRequestEntity();
    request.token = await dataManager.getAuthToken();
    request.userId = int.parse(await dataManager.getUserId() ?? "0");
    request.taskId = taskId;
    request.folderId = folderId;
    request.signatureId = signature.signatureId?.toInt();
    request.signedBy = signature.signedBy;
    request.cannotUploadMandatory = signature.cannotUploadMandatory;
    request.formId = signature.formId?.toInt();
    request.questionId = signature.questionId?.toInt();
    request.deviceuid = await dataManager.getOrCreateDeviceId();

    if (signature.cannotUploadMandatory == true) {
      request.signatureBlob = "-1";
    } else {
      // Use localPath if available, otherwise use signatureUrl
      final signaturePath = signature.localPath ?? signature.signatureUrl ?? "";
      request.signatureBlob =
          await ImageStorageUtils.encodeFileToBase64(signaturePath);
    }

    return request;
  }

  /// Create sync pic info request entity for photo metadata sync
  static Future<sync_pic.SyncPicInfoRequestEntity> createSyncPicInfoRequest({
    required List<TaskDetail> tasks,
  }) async {
    final dataManager = sl<DataManager>();

    final syncPicRequest = sync_pic.SyncPicInfoRequestEntity();
    final List<sync_pic.Task> tasksSync = [];

    for (var task in tasks) {
      final List<sync_pic.PhotoFolder> photoFolderList = [];

      for (var photoFolder in task.photoFolder ?? []) {
        final deletePhotosIds = <int>[];

        for (var photo in photoFolder.photos ?? []) {
          if (photo.userDeletedPhoto == true) {
            deletePhotosIds.add(photo.photoId?.toInt() ?? 0);
          }
        }

        if (deletePhotosIds.isNotEmpty) {
          photoFolderList.add(sync_pic.PhotoFolder(
            folderId: photoFolder.folderId?.toInt(),
            deletePhotosIds: deletePhotosIds.join(","),
          ));
        }
      }

      if (photoFolderList.isNotEmpty) {
        tasksSync.add(sync_pic.Task(
          taskId: task.taskId?.toInt(),
          uploadPhotosSuccess: true,
          modifiedTimeStampPhotos: task.modifiedTimeStampPhotos,
          photoFolder: photoFolderList,
        ));
      }
    }

    syncPicRequest.tasks = tasksSync;
    syncPicRequest.token = await dataManager.getAuthToken();
    syncPicRequest.userId = int.parse(await dataManager.getUserId() ?? "0");
    syncPicRequest.deviceuid = await dataManager.getOrCreateDeviceId();

    return syncPicRequest;
  }

  /// Create sync signature info request entity for signature metadata sync
  static Future<sync_sign.SyncSignInfoRequestEntity>
      createSyncSignatureInfoRequest({
    required List<TaskDetail> tasks,
  }) async {
    final dataManager = sl<DataManager>();

    final syncSignRequest = sync_sign.SyncSignInfoRequestEntity();
    final List<sync_sign.Task> tasksSync = [];

    for (var task in tasks) {
      final List<sync_sign.SignatureFolder> signatureFolderList = [];

      for (var signatureFolder in task.signatureFolder ?? []) {
        final deleteSignatureIds = <int>[];

        for (var signature in signatureFolder.signatures ?? []) {
          if (signature.userDeletedSignature == true) {
            deleteSignatureIds.add(signature.signatureId?.toInt() ?? 0);
          }
        }

        if (deleteSignatureIds.isNotEmpty) {
          signatureFolderList.add(sync_sign.SignatureFolder(
            folderId: signatureFolder.folderId?.toInt(),
            deleteSignaturesIds: deleteSignatureIds.join(","),
          ));
        }
      }

      if (signatureFolderList.isNotEmpty) {
        tasksSync.add(sync_sign.Task(
          taskId: task.taskId?.toInt(),
          uploadSignatureSuccess: true,
          modifiedTimeStampSignatures: task.modifiedTimeStampSignatures,
          signatureFolder: signatureFolderList,
        ));
      }
    }

    syncSignRequest.tasks = tasksSync;
    syncSignRequest.token = await dataManager.getAuthToken();
    syncSignRequest.userId = int.parse(await dataManager.getUserId() ?? "0");
    syncSignRequest.deviceuid = await dataManager.getOrCreateDeviceId();

    return syncSignRequest;
  }

  /// Get all photos that need to be uploaded from tasks
  static List<Map<String, dynamic>> getPhotosToUpload(List<TaskDetail> tasks) {
    final List<Map<String, dynamic>> photosToUpload = [];

    for (var task in tasks) {
      for (var photoFolder in task.photoFolder ?? []) {
        for (var photo in photoFolder.photos ?? []) {
          // Only include photos that haven't been deleted and have content
          if (photo.userDeletedPhoto != true &&
              (photo.isEdited == true || photo.localPath != null)) {
            photosToUpload.add({
              'photo': photo,
              'taskId': task.taskId?.toInt() ?? 0,
              'folderId': photoFolder.folderId?.toInt() ?? 0,
            });
          }
        }
      }
    }

    return photosToUpload;
  }

  /// Get all signatures that need to be uploaded from tasks
  static List<Map<String, dynamic>> getSignaturesToUpload(
      List<TaskDetail> tasks) {
    final List<Map<String, dynamic>> signaturesToUpload = [];

    for (var task in tasks) {
      for (var signatureFolder in task.signatureFolder ?? []) {
        for (var signature in signatureFolder.signatures ?? []) {
          // Only include signatures that haven't been deleted and have content
          if (signature.userDeletedSignature != true &&
              (signature.isEdited == true || signature.localPath != null)) {
            signaturesToUpload.add({
              'signature': signature,
              'taskId': task.taskId?.toInt() ?? 0,
              'folderId': signatureFolder.folderId?.toInt() ?? 0,
            });
          }
        }
      }
    }

    return signaturesToUpload;
  }

  /// Check if a given `photoId` already exists inside any of the photo folders
  /// of the provided [task].
  ///
  /// The API that handles `/api/send_task_pic_v4_11` returns the definitive
  /// `photoId` generated by the server.  When the same image is uploaded twice
  /// (user selects the same image again, or device retries a previous upload),
  /// the backend may respond with an **existing** `photoId` instead of a newly
  /// created one.  This helper makes it easy to detect that situation so that
  /// callers can decide to ignore the duplicate or clean up the temporary
  /// local record.
  ///
  /// Returns `true` if any photo inside `task.photoFolder` has `photoId` equal
  /// to [serverPhotoId]; otherwise returns `false`.
  static bool isDuplicatePhoto({
    required TaskDetail task,
    required int serverPhotoId,
  }) {
    if (serverPhotoId == 0) return false;

    for (final photoFolder in task.photoFolder ?? []) {
      for (final photo in photoFolder.photos ?? []) {
        if ((photo.photoId ?? 0).toInt() == serverPhotoId) {
          return true;
        }
      }
    }
    return false;
  }

  /// Handles photo upload response with duplicate detection and local storage optimization
  ///
  /// This method processes the server response from photo upload API and:
  /// 1. Checks if the uploaded photo is a duplicate (already exists in database)
  /// 2. If duplicate: Deletes the duplicate photo record from database
  /// 3. If not duplicate: Updates the local photo record with server data and deletes local file
  ///
  /// Parameters:
  /// - [uploadResponse]: The response from the photo upload API
  /// - [taskId]: The ID of the task containing the photo
  /// - [originalLocalPath]: The original local path of the uploaded photo (for matching)
  ///
  /// Returns `true` if processing was successful, `false` otherwise
  static Future<bool> handlePhotoUploadResponse({
    required UploadPhotoResponseEntity uploadResponse,
    required int taskId,
    required String originalLocalPath,
  }) async {
    try {
      final serverPhoto = uploadResponse.data;
      if (serverPhoto == null) {
        logger('Invalid server photo response: data is null');
        return false;
      }

      final serverPhotoId = serverPhoto.photoId?.toInt() ?? 0;

      if (serverPhotoId == 0) {
        logger('Invalid server photo ID received');
        return false;
      }

      // Check for duplicate photo using PhotoUtils
      final isDuplicate = await PhotoUtils.photoExists(
        taskId: taskId,
        photoId: serverPhotoId,
      );

      if (isDuplicate) {
        // Handle duplicate: delete the duplicate photo record
        logger(
            'Duplicate photo detected with ID: $serverPhotoId, deleting local record');
        return await PhotoUtils.deletePhotoRecord(
          taskId: taskId,
          localPath: originalLocalPath,
          deleteLocalFile: true,
        );
      } else {
        // Handle successful upload: update photo record and delete local file
        logger('Updating photo record with server data for ID: $serverPhotoId');
        return await PhotoUtils.updatePhotoWithServerData(
          taskId: taskId,
          localPath: originalLocalPath,
          photoId: serverPhotoId,
          photoUrl: serverPhoto.photoUrl,
          caption: serverPhoto.photoCaption,
          cannotUploadMandatory: serverPhoto.cannotUploadMandatory,
          modifiedTimeStamp: serverPhoto.modifiedTimeStampPhoto,
          deleteLocalFile: true,
        );
      }
    } catch (e) {
      logger('Error handling photo upload response: $e');
      return false;
    }
  }

  /// Handles signature upload response with duplicate detection and local storage optimization
  ///
  /// This method processes the server response from signature upload API and:
  /// 1. Checks if the uploaded signature is a duplicate (already exists in database)
  /// 2. If duplicate: Deletes the duplicate signature record from database
  /// 3. If not duplicate: Updates the local signature record with server data and deletes local file
  ///
  /// Parameters:
  /// - [uploadResponse]: The response from the signature upload API
  /// - [taskId]: The ID of the task containing the signature
  /// - [originalLocalPath]: The original local path of the uploaded signature (for matching)
  ///
  /// Returns `true` if processing was successful, `false` otherwise
  static Future<bool> handleSignatureUploadResponse({
    required UploadSignatureResponseEntity uploadResponse,
    required int taskId,
    required String originalLocalPath,
  }) async {
    try {
      final serverSignature = uploadResponse.data;
      if (serverSignature == null) {
        logger('Invalid server signature response: data is null');
        return false;
      }

      final serverSignatureId = serverSignature.signatureId?.toInt() ?? 0;

      if (serverSignatureId == 0) {
        logger('Invalid server signature ID received');
        return false;
      }

      // Check for duplicate signature using SignatureUtils
      final isDuplicate = await SignatureUtils.signatureExists(
        taskId: taskId,
        signatureId: serverSignatureId,
      );

      if (isDuplicate) {
        // Handle duplicate: delete the duplicate signature record
        logger(
            'Duplicate signature detected with ID: $serverSignatureId, deleting local record');
        return await SignatureUtils.deleteSignatureRecord(
          taskId: taskId,
          localPath: originalLocalPath,
          deleteLocalFile: true,
        );
      } else {
        // Handle successful upload: update signature record and delete local file
        logger(
            'Updating signature record with server data for ID: $serverSignatureId');
        return await SignatureUtils.updateSignatureWithServerData(
          taskId: taskId,
          localPath: originalLocalPath,
          signatureId: serverSignatureId,
          signatureUrl: serverSignature.signatureUrl,
          signedBy: serverSignature.signedBy,
          cannotUploadMandatory: serverSignature.cannotUploadMandatory,
          modifiedTimeStamp: serverSignature.modifiedTimeStampSignature,
          deleteLocalFile: true,
        );
      }
    } catch (e) {
      logger('Error handling signature upload response: $e');
      return false;
    }
  }

  /// Create submit report request entity from task data
  ///
  /// This method prepares the request body for the submit report API,
  /// taking inspiration from the submitReportData() method in syncing.dart.
  /// It structures the request with all necessary task data including forms,
  /// question answers, and followup tasks.
  ///
  /// Parameters:
  /// - [task]: The task detail containing all data to be submitted
  ///
  /// Returns a properly structured SubmitReportRequestEntity
  static Future<submit_report.SubmitReportRequestEntity>
      createSubmitReportRequest({
    required TaskDetail task,
  }) async {
    final dataManager = sl<DataManager>();
    final String actualDeviceUid = await dataManager.getOrCreateDeviceId();
    const String actualAppVersion = AppConstants.appVersion;

    final request = submit_report.SubmitReportRequestEntity();
    request.token = await dataManager.getAuthToken();
    request.userId = await dataManager.getUserId();
    request.deviceUid = actualDeviceUid;
    request.appversion = actualAppVersion;
    request.taskId = task.taskId.toString();
    request.comment = task.comment;
    request.minutes = task.minutes?.toInt();
    request.timerMinutes = 0;
    request.claimableKms = task.claimableKms?.toInt() ?? 0;
    request.pages = task.pages?.toInt() ?? 0;
    request.taskStatus = task.taskStatus;
    request.submissionState = task.submissionState?.toInt();
    request.taskCommencementTimeStamp =
        task.taskCommencementTimeStamp ?? AppConstants.minDateTime;
    request.taskStoppedTimeStamp =
        task.taskStoppedTimeStamp ?? AppConstants.minDateTime;
    request.scheduledTimeStamp = task.scheduledTimeStamp;
    request.submissionTimeStamp = DateTime.now();
    request.startTaskLatitude = task.taskLatitude?.toInt() ?? 0;
    request.startTaskLongitude = task.taskLongitude?.toInt() ?? 0;
    request.taskLatitude = task.latitude?.toInt() ?? 0;
    request.taskLongitude = task.longitude?.toInt() ?? 0;
    request.budgetCalculated =
        FormUtils.calculateBudget(task.taskId!.toInt())?.tempBudget ?? 0;

    // Initialize lists
    request.forms = [];
    request.followupTasks = [];
    request.resumePauseItems = [];

    // Process forms and question answers
    for (var form in task.forms ?? []) {
      var formPost = Form();
      formPost.formId = form.formId;
      formPost.questionAnswers = [];

      for (var questionAnswer in form.questionAnswers ?? []) {
        if ((questionAnswer.isComment == false) ||
            (questionAnswer.isComment == true &&
                questionAnswer.measurementTextResult != null)) {
          var questionAnswerPost = QuestionAnswer(
            questionId: questionAnswer.questionId,
            questionpartId: questionAnswer.questionpartId,
            questionPartMultiId: questionAnswer.questionPartMultiId,
            measurementId: questionAnswer.measurementId,
            measurementTypeId: questionAnswer.measurementTypeId,
            measurementOptionId: questionAnswer.measurementOptionId,
            measurementOptionIds: questionAnswer.measurementOptionIds,
            measurementTextResult: questionAnswer.measurementTextResult,
            isComment: questionAnswer.isComment,
            commentTypeId: questionAnswer.commentTypeId,
          );
          formPost.questionAnswers?.add(questionAnswerPost);
        }
      }

      if (formPost.questionAnswers?.isNotEmpty == true) {
        request.forms?.add(formPost);
      }
    }

    // Process followup tasks
    for (var followupTask in task.followupTasks ?? []) {
      var followupTaskPost = submit_report.FollowupTask();
      followupTaskPost.taskId = task.taskId.toString();
      followupTaskPost.visitDate = followupTask.selectedVisitDate;
      followupTaskPost.followupTypeId = 0;
      followupTaskPost.followupItemId = 0;
      followupTaskPost.budget = followupTask.selectedBudget?.toInt();
      followupTaskPost.scheduleNote = followupTask.selectedScheduleNote;
      followupTaskPost.followupNumber = followupTask.followupNumber?.toInt();
      request.followupTasks?.add(followupTaskPost);
    }

    // Process resume pause items
    for (var resumePauseItem in task.resumePauseItems ?? []) {
      var resumePauseItemPost = ResumePauseItem();
      resumePauseItemPost.resumeOrderID = resumePauseItem.resumeOrderID;
      resumePauseItemPost.resumeDate = resumePauseItem.resumeDate;
      resumePauseItemPost.pauseDate = resumePauseItem.pauseDate;
      request.resumePauseItems?.add(resumePauseItemPost);
    }

    return request;
  }

  /// Get all tasks that need to be submitted (have sync pending status)
  ///
  /// Returns a list of TaskDetail entities that are marked for sync
  static List<TaskDetail> getTasksToSubmit() {
    final realm = RealmDatabase.instance.realm;
    final taskModels = realm.all<TaskDetailModel>();

    // Convert models to entities
    final tasks =
        taskModels.map((model) => TaskDetailMapper.toEntity(model)).toList();

    return tasks.where((task) => task.syncPending == true).toList();
  }

  Future<RefreshFormsEntity> prepareRefreshTasksRequest(String taskId) async {
    var dataManager = sl<DataManager>();
    var token = await dataManager.getAuthToken();
    var userId = await dataManager.getUserId();
    var deviceUid = await dataManager.getOrCreateDeviceId();
    var appversion = AppConstants.appVersion;
    TaskDetailModel taskModel = sl<RealmDatabase>()
        .realm
        .query<TaskDetailModel>('taskId == $taskId')
        .first;
    TaskDetail task = TaskDetailMapper.toEntity(taskModel);
    var request = RefreshFormsEntity(
      token: token,
      userId: userId,
      deviceUid: deviceUid,
      appversion: appversion,
    );
    request.tasks = <RefreshTaskEntity>[];
    var photoTypeIds = task.photoFolder
        ?.map((e) => e.folderId)
        .where((id) => id != null)
        .cast<int>()
        .toList();
    var signatureTypeIds = task.signatureFolder
        ?.map((e) => e.folderId)
        .where((id) => id != null)
        .cast<int>()
        .toList();
    var tasks = RefreshTaskEntity(
      taskId: taskId,
      scheduledTimeStamp: task.scheduledTimeStamp,
      submissionTimeStamp: task.submissionTimeStamp,
      modifiedTimeStampTask: task.modifiedTimeStampTask,
      modifiedTimeStampPhotos: task.modifiedTimeStampPhotos,
      modifiedTimeStampPhototypes: task.modifiedTimeStampPhototypes,
      modifiedTimeStampForms: task.modifiedTimeStampForms,
      modifiedTimeStampDocuments: task.modifiedTimeStampDocuments,
      modifiedTimeStampSignatures:
          task.modifiedTimeStampSignatures ?? AppConstants.minDateTime,
      modifiedTimeStampSignaturetypes:
          task.modifiedTimeStampSignaturetypes ?? AppConstants.minDateTime,
      forceImportFollowupTask: false,
      phototypeIds: photoTypeIds,
      signaturetypeIds: signatureTypeIds,
    );
    request.tasks?.add(tasks);
    return request;
  }

  /// Process TasksResponseEntity and update local database
  ///
  /// This method handles all response fields from the getTasks API:
  /// - delete_task_ids: Delete tasks from local database
  /// - update_tasks_documents: Update document-related data for existing tasks
  /// - update_tasks_forms: Update form-related data for existing tasks
  /// - update_task_members: Update task member data
  /// - update_tasks_tasks: Update general task data for existing tasks
  /// - update_tasks_photos: Update photo-related data for existing tasks
  /// - update_phototypes: Update photo type data
  /// - add_phototypes: Add new photo types
  /// - delete_phototypes: Delete photo types
  /// - add_tasks: Add new tasks to local database
  /// - update_tasks_submission: Update submission-related data for existing tasks
  /// - update_tasks_signatures: Update signature-related data for existing tasks
  /// - update_signature_types: Update signature type data
  /// - add_signature_types: Add new signature types
  /// - delete_signature_types: Delete signature types
  ///
  /// Returns true if processing was successful, false otherwise
  static Future<bool> processTasksResponse(TasksResponseEntity response) async {
    try {
      final realm = sl<RealmDatabase>().realm;
      // Track tasks that need budget recalculation (following Java implementation)
      final taskIDsToCalculateBudget = <String>{};

      // Process each response field in order following process_core.md 15 steps
      // Step 1: Delete tasks
      await _processDeleteTaskIds(realm, response.deleteTaskIds);
      // Step 2: Update task documents
      await _processUpdateTasksDocuments(realm, response.updateTasksDocuments);
      // Step 3: Update tasks forms
      await _processUpdateTasksForms(realm, response.updateTasksForms);
      // Step 4: Update task members
      await _processUpdateTaskMembers(realm, response.updateTaskMembers);
      // Step 5: Update tasks information/tasks
      await _processUpdateTasksTasks(
          realm, response.updateTasksTasks, taskIDsToCalculateBudget);
      // Step 6: Update tasks photos
      await _processUpdateTasksPhotos(realm, response.updateTasksPhotos);
      // Step 7: Update phototypes
      await _processUpdatePhototypes(realm, response.updatePhototypes);
      // Step 8: Add phototypes
      await _processAddPhototypes(realm, response.addPhototypes);
      // Step 9: Delete phototypes
      await _processDeletePhototypes(realm, response.deletePhototypes);
      // Step 10: Add new tasks
      await _processAddTasks(
          realm, response.addTasks, taskIDsToCalculateBudget);
      // Step 11: Update task submission
      await _processUpdateTasksSubmission(
          realm, response.updateTasksSubmission, taskIDsToCalculateBudget);
      // Step 12: Update tasks signatures
      await _processUpdateTasksSignatures(
          realm, response.updateTasksSignatures);
      // Step 13: Update signature types
      await _processUpdateSignatureTypes(realm, response.updateSignatureTypes);
      // Step 14: Add signature types
      await _processAddSignatureTypes(realm, response.addSignatureTypes);
      // Step 15: Delete signature types
      await _processDeleteSignatureTypes(realm, response.deleteSignatureTypes);

      // Calculate budget for tasks that need it (following Java implementation)
      logger(
          '💰 Processing ${taskIDsToCalculateBudget.length} tasks for budget calculation');
      for (final taskId in taskIDsToCalculateBudget) {
        try {
          final taskIdInt = int.tryParse(taskId);
          if (taskIdInt != null) {
            FormUtils.calculateBudget(taskIdInt);
            logger('💰 Budget calculated for task: $taskId');
          }
        } catch (e) {
          logger('❌ Error calculating budget for task $taskId: $e');
        }
      }

      // Download files for offline use
      await _downloadFilesForOfflineUse();

      logger('✅ TasksResponse processing completed successfully');
      return true;
    } catch (e) {
      logger('❌ Error processing TasksResponse: $e');
      return false;
    }
  }

  /// Process delete_task_ids: Remove tasks from local database
  static Future<void> _processDeleteTaskIds(
      Realm realm, List<String>? deleteTaskIds) async {
    if (deleteTaskIds == null || deleteTaskIds.isEmpty) return;

    logger('🗑️ Processing ${deleteTaskIds.length} task deletions');

    realm.write(() {
      for (final taskIdStr in deleteTaskIds) {
        final taskId = int.tryParse(taskIdStr);
        if (taskId == null) {
          logger('⚠️ Invalid task ID format: $taskIdStr');
          continue;
        }

        final task =
            realm.query<TaskDetailModel>('taskId == \$0', [taskId]).firstOrNull;
        if (task != null) {
          realm.delete(task);
          logger('🗑️ Deleted task: $taskId');
        } else {
          logger('⚠️ Task not found for deletion: $taskId');
        }
      }
    });
  }

  /// Process update_tasks_documents: Update document-related data for existing tasks
  ///
  /// This method mirrors the Java implementation exactly, including:
  /// - Individual document and file comparison
  /// - Selective file deletion based on modification dates
  /// - Proper error handling for each operation
  static Future<void> _processUpdateTasksDocuments(
      Realm realm, List<TaskDetail>? updateTasksDocuments) async {
    if (updateTasksDocuments == null || updateTasksDocuments.isEmpty) return;

    logger('📄 Processing ${updateTasksDocuments.length} document updates');

    realm.write(() {
      for (final taskUpdate in updateTasksDocuments) {
        try {
          final taskId = taskUpdate.taskId?.toInt();
          if (taskId == null) continue;

          final existingTask = realm
              .query<TaskDetailModel>('taskId == \$0', [taskId]).firstOrNull;

          if (existingTask != null) {
            // Create server task model to get the mapped documents
            final serverTaskModel = TaskDetailMapper.toModel(taskUpdate, 0);

            // Compare server documents with local documents (mirroring Java logic)
            for (final serverDocumentModel in serverTaskModel.documents) {
              for (final serverFileModel in serverDocumentModel.files) {
                // Find local document by document ID
                final localDocumentModel = existingTask.documents
                    .where((doc) =>
                        doc.documentId == serverDocumentModel.documentId)
                    .firstOrNull;

                if (localDocumentModel != null) {
                  // Find local file by file ID
                  final localFileModel = localDocumentModel.files
                      .where((file) => file.fileId == serverFileModel.fileId)
                      .firstOrNull;

                  if (localFileModel != null) {
                    try {
                      // Compare file IDs and modification dates
                      if (serverFileModel.fileId == localFileModel.fileId) {
                        // Check if server file is newer than local file
                        final serverModified =
                            serverFileModel.modifiedTimeStampFile;
                        final localModified =
                            localFileModel.modifiedTimeStampFile;

                        if (localModified == null ||
                            (serverModified != null &&
                                serverModified.isAfter(localModified))) {
                          // Server has newer file - delete local file
                          // TODO: Uncomment when file saving is implemented
                          // try {
                          //   if (localFileModel.fileLocalPath != null &&
                          //       localFileModel.fileLocalPath!.isNotEmpty) {
                          //     final localFile = File(localFileModel.fileLocalPath!);
                          //     if (await localFile.exists()) {
                          //       await localFile.delete();
                          //     }
                          //   }
                          // } catch (e) {
                          //   logger('Error deleting local file: $e');
                          // }
                        }
                      }
                    } catch (e) {
                      logger('Error comparing file dates: $e');
                    }
                  }
                }
              }
            }

            // Delete all existing document records and replace with server data
            _removeTaskDocumentsFromRealm(existingTask, false);

            // Copy new document data from server
            existingTask.documents.addAll(serverTaskModel.documents);
            existingTask.modifiedTimeStampDocuments =
                taskUpdate.modifiedTimeStampDocuments;

            logger('📄 Updated documents for task: $taskId');
          } else {
            logger('⚠️ Task not found for document update: $taskId');
          }
        } catch (e) {
          logger('Error processing document update: $e');
          // Continue with next task even if this one fails
        }
      }
    });
  }

  /// Process update_tasks_forms: Update form-related data for existing tasks
  /// This method now uses selective updates instead of wholesale replacement
  static Future<void> _processUpdateTasksForms(
      Realm realm, List<TaskDetail>? updateTasksForms) async {
    if (updateTasksForms == null || updateTasksForms.isEmpty) {
      logger('SYNC_FORMS_001: No forms to update - returning early');
      return;
    }

    final timestamp = DateTime.now().toIso8601String();
    logger(
        'SYNC_FORMS_002: [$timestamp] Starting form updates processing for ${updateTasksForms.length} tasks');

    realm.write(() {
      for (int taskIndex = 0;
          taskIndex < updateTasksForms.length;
          taskIndex++) {
        final taskUpdate = updateTasksForms[taskIndex];
        final taskId = taskUpdate.taskId?.toInt();

        logger(
            'SYNC_FORMS_003: [$taskIndex/${updateTasksForms.length}] Processing task update for taskId: $taskId');

        if (taskId == null) {
          logger('SYNC_FORMS_004: Skipping task update - taskId is null');
          continue;
        }

        final existingTask =
            realm.query<TaskDetailModel>('taskId == \$0', [taskId]).firstOrNull;

        if (existingTask == null) {
          logger('SYNC_FORMS_005: Task not found for form update: $taskId');
          continue;
        }

        logger(
            'SYNC_FORMS_006: Found existing task $taskId with ${existingTask.forms.length} local forms');

        // Log existing question answers BEFORE any modifications
        _logExistingQuestionAnswersState(existingTask, 'BEFORE_UPDATE');

        // Log form counter fields before update
        logger(
            'SYNC_FORMS_007: Form counters BEFORE update - taskId: $taskId, ctFormsTotalCnt: ${existingTask.ctFormsTotalCnt}, ctFormsCompletedCnt: ${existingTask.ctFormsCompletedCnt}, kTotal: ${existingTask.kTotal}, kCompleted: ${existingTask.kCompleted}');

        // Update form counter fields
        final oldCtFormsTotalCnt = existingTask.ctFormsTotalCnt;
        final oldCtFormsCompletedCnt = existingTask.ctFormsCompletedCnt;
        final oldKTotal = existingTask.kTotal;
        final oldKCompleted = existingTask.kCompleted;

        existingTask.ctFormsTotalCnt = taskUpdate.ctFormsTotalCnt?.toInt();
        existingTask.ctFormsCompletedCnt =
            taskUpdate.ctFormsCompletedCnt?.toInt();
        existingTask.kTotal = taskUpdate.kTotal?.toInt();
        existingTask.kCompleted = taskUpdate.kCompleted?.toInt();

        logger(
            'SYNC_FORMS_008: Form counters AFTER update - taskId: $taskId, ctFormsTotalCnt: $oldCtFormsTotalCnt -> ${existingTask.ctFormsTotalCnt}, ctFormsCompletedCnt: $oldCtFormsCompletedCnt -> ${existingTask.ctFormsCompletedCnt}, kTotal: $oldKTotal -> ${existingTask.kTotal}, kCompleted: $oldKCompleted -> ${existingTask.kCompleted}');

        if (taskUpdate.forms != null) {
          logger(
              'SYNC_FORMS_009: Processing ${taskUpdate.forms!.length} server forms for task $taskId');

          // Create a temporary task model to get the mapped server forms
          logger(
              'SYNC_FORMS_010: Creating temporary task model for server form mapping');
          final tempTaskModel = TaskDetailMapper.toModel(taskUpdate, 0);
          final serverForms = tempTaskModel.forms;
          logger(
              'SYNC_FORMS_011: Mapped ${serverForms.length} server forms from TaskDetailMapper');

          // Log server forms structure
          for (int serverFormIndex = 0;
              serverFormIndex < serverForms.length;
              serverFormIndex++) {
            final serverForm = serverForms[serverFormIndex];
            logger(
                'SYNC_FORMS_012: Server form [$serverFormIndex] - formId: ${serverForm.formId}, questionAnswers count: ${serverForm.questionAnswers.length}, questions count: ${serverForm.questions.length}');

            // Log server form question answers in detail
            if (serverForm.questionAnswers.isNotEmpty) {
              logger(
                  'SYNC_FORMS_013: Server form ${serverForm.formId} question answers:');
              for (int qaIndex = 0;
                  qaIndex < serverForm.questionAnswers.length;
                  qaIndex++) {
                final qa = serverForm.questionAnswers[qaIndex];
                logger(
                    'SYNC_FORMS_014: Server QA [$qaIndex] - questionId: ${qa.questionId}, measurementId: ${qa.measurementId}, measurementTextResult: ${qa.measurementTextResult}, measurementOptionId: ${qa.measurementOptionId}');
              }
            }
          }

          // Process each server form for selective updates
          for (int serverFormIndex = 0;
              serverFormIndex < serverForms.length;
              serverFormIndex++) {
            final serverForm = serverForms[serverFormIndex];
            logger(
                'SYNC_FORMS_015: Processing server form [$serverFormIndex] - formId: ${serverForm.formId}');

            // Find matching local form
            final localForm = existingTask.forms
                .where((f) => f.formId == serverForm.formId)
                .firstOrNull;

            if (localForm != null) {
              logger(
                  'SYNC_FORMS_016: Found matching local form ${serverForm.formId} with ${localForm.questionAnswers.length} existing question answers');

              // Log existing local question answers BEFORE any changes
              if (localForm.questionAnswers.isNotEmpty) {
                logger(
                    'SYNC_FORMS_017: Local form ${localForm.formId} EXISTING question answers BEFORE update:');
                for (int qaIndex = 0;
                    qaIndex < localForm.questionAnswers.length;
                    qaIndex++) {
                  final qa = localForm.questionAnswers[qaIndex];
                  logger(
                      'SYNC_FORMS_018: Local QA BEFORE [$qaIndex] - questionId: ${qa.questionId}, measurementId: ${qa.measurementId}, measurementTextResult: ${qa.measurementTextResult}, measurementOptionId: ${qa.measurementOptionId}');
                }
              }

              // Form exists - update individual fields
              logger(
                  'SYNC_FORMS_019: Updating individual fields for existing form ${serverForm.formId}');
              FormUtilsCascadeDeletion.updateFormFields(localForm, serverForm);

              // Log question answers count before cleanup
              final questionAnswersCountBeforeCleanup =
                  localForm.questionAnswers.length;
              logger(
                  'SYNC_FORMS_020: Question answers count BEFORE cleanup: $questionAnswersCountBeforeCleanup');

              // Clean up existing form questions before updating structure
              logger(
                  'SYNC_FORMS_021: Removing existing form questions from realm for form ${serverForm.formId}');
              FormUtilsCascadeDeletion.removeFormQuestionsFromRealm(localForm);

              // Log question answers count after cleanup
              final questionAnswersCountAfterCleanup =
                  localForm.questionAnswers.length;
              logger(
                  'SYNC_FORMS_022: Question answers count AFTER cleanup: $questionAnswersCountAfterCleanup (was: $questionAnswersCountBeforeCleanup)');

              // Update form structure with server questions
              logger(
                  'SYNC_FORMS_023: Adding ${serverForm.questions.length} server questions to local form ${serverForm.formId}');
              localForm.questions.addAll(serverForm.questions);

              // Check if question answers are preserved or need to be restored
              final questionAnswersCountAfterQuestionUpdate =
                  localForm.questionAnswers.length;
              logger(
                  'SYNC_FORMS_024: Question answers count AFTER adding server questions: $questionAnswersCountAfterQuestionUpdate');

              // Log final state of question answers for this form
              if (localForm.questionAnswers.isNotEmpty) {
                logger(
                    'SYNC_FORMS_025: Local form ${localForm.formId} FINAL question answers AFTER update:');
                for (int qaIndex = 0;
                    qaIndex < localForm.questionAnswers.length;
                    qaIndex++) {
                  final qa = localForm.questionAnswers[qaIndex];
                  logger(
                      'SYNC_FORMS_026: Local QA FINAL [$qaIndex] - questionId: ${qa.questionId}, measurementId: ${qa.measurementId}, measurementTextResult: ${qa.measurementTextResult}, measurementOptionId: ${qa.measurementOptionId}');
                }
              } else {
                // Only log as warning if we had question answers before cleanup but lost them after
                if (questionAnswersCountBeforeCleanup > 0) {
                  logger(
                      'SYNC_FORMS_027: WARNING - Local form ${localForm.formId} lost $questionAnswersCountBeforeCleanup question answers during update! This indicates actual data loss.');
                } else {
                  logger(
                      'SYNC_FORMS_027: INFO - Local form ${localForm.formId} has no question answers (expected for new/empty forms).');
                }
              }

              logger(
                  'SYNC_FORMS_028: Updated existing form: ${serverForm.formId}');
            } else {
              logger(
                  'SYNC_FORMS_029: Local form not found - adding new form ${serverForm.formId} with ${serverForm.questionAnswers.length} question answers');
              existingTask.forms.add(serverForm);
              logger('SYNC_FORMS_030: Added new form: ${serverForm.formId}');
            }
          }

          // Check for forms that need to be deleted (exist locally but not on server)
          logger('SYNC_FORMS_031: Checking for local forms to delete');
          final localFormsToDelete = <FormModel>[];
          for (final localForm in existingTask.forms) {
            if (!FormUtilsCascadeDeletion.formExistsInServerList(
                localForm, serverForms)) {
              localFormsToDelete.add(localForm);
              logger(
                  'SYNC_FORMS_032: Marking local form ${localForm.formId} for deletion (not found on server)');
            }
          }

          // Safely delete forms that are no longer on server
          logger(
              'SYNC_FORMS_033: Deleting ${localFormsToDelete.length} forms not found on server');
          for (final formToDelete in localFormsToDelete) {
            logger(
                'SYNC_FORMS_034: Deleting form not found on server: ${formToDelete.formId} (had ${formToDelete.questionAnswers.length} question answers)');
            FormUtilsCascadeDeletion.removeFormFromTask(
                existingTask, formToDelete);
          }
        } else {
          logger(
              'SYNC_FORMS_035: No server forms provided for task $taskId - skipping form processing');
        }

        // Update timestamp
        final oldTimestamp = existingTask.modifiedTimeStampForms;
        existingTask.modifiedTimeStampForms = taskUpdate.modifiedTimeStampForms;
        logger(
            'SYNC_FORMS_036: Updated modifiedTimeStampForms for task $taskId: $oldTimestamp -> ${existingTask.modifiedTimeStampForms}');

        // Log existing question answers AFTER all modifications
        _logExistingQuestionAnswersState(existingTask, 'AFTER_UPDATE');

        logger('SYNC_FORMS_037: Completed forms update for task: $taskId');
      }
    });

    logger(
        'SYNC_FORMS_038: [$timestamp] Completed form updates processing for all tasks');
  }

  /// Helper method to log the current state of question answers for debugging
  static void _logExistingQuestionAnswersState(
      TaskDetailModel task, String stage) {
    logger(
        'QA_DATA_001: [$stage] Question answers state for task ${task.taskId}:');

    int totalQuestionAnswers = 0;
    for (int formIndex = 0; formIndex < task.forms.length; formIndex++) {
      final form = task.forms[formIndex];
      final qaCount = form.questionAnswers.length;
      totalQuestionAnswers += qaCount;

      logger(
          'QA_DATA_002: [$stage] Form ${form.formId} has $qaCount question answers');

      if (qaCount > 0) {
        for (int qaIndex = 0;
            qaIndex < form.questionAnswers.length;
            qaIndex++) {
          final qa = form.questionAnswers[qaIndex];
          logger(
              'QA_DATA_003: [$stage] Form ${form.formId} QA[$qaIndex] - questionId: ${qa.questionId}, questionpartId: ${qa.questionpartId}, measurementId: ${qa.measurementId}, measurementTextResult: "${qa.measurementTextResult}", measurementOptionId: ${qa.measurementOptionId}, measurementOptionIds: "${qa.measurementOptionIds}", isComment: ${qa.isComment}');
        }
      }
    }

    logger(
        'QA_DATA_004: [$stage] Task ${task.taskId} has TOTAL $totalQuestionAnswers question answers across ${task.forms.length} forms');
  }

  /// Process update_task_members: Update member/helper data for existing tasks
  static Future<void> _processUpdateTaskMembers(
      Realm realm, List<TaskMembersHelper>? updateTaskMembers) async {
    if (updateTaskMembers == null || updateTaskMembers.isEmpty) return;

    logger('👥 Processing ${updateTaskMembers.length} member updates');

    realm.write(() {
      for (final memberUpdate in updateTaskMembers) {
        final taskId = memberUpdate.taskId;
        if (taskId == null) continue;

        final existingTask =
            realm.query<TaskDetailModel>('taskId == \$0', [taskId]).firstOrNull;
        if (existingTask != null) {
          // Update member-related fields
          existingTask.taskmembers.clear();
          if (memberUpdate.taskMembers != null) {
            // Create a temporary task detail with the members
            final tempTask = TaskDetail(taskmembers: memberUpdate.taskMembers);
            final tempTaskModel = TaskDetailMapper.toModel(tempTask, 0);
            existingTask.taskmembers.addAll(tempTaskModel.taskmembers);
          }
          // existingTask.modifiedTimeStampMembers = DateTime.now();

          logger('👥 Updated members for task: $taskId');
        } else {
          logger('⚠️ Task not found for member update: $taskId');
        }
      }
    });
  }

  /// Process update_tasks_tasks: Update general task data for existing tasks
  static Future<void> _processUpdateTasksTasks(
      Realm realm,
      List<TaskDetail>? updateTasksTasks,
      Set<String> taskIDsToCalculateBudget) async {
    if (updateTasksTasks == null || updateTasksTasks.isEmpty) return;

    logger('📋 Processing ${updateTasksTasks.length} task updates');

    realm.write(() {
      for (final taskUpdate in updateTasksTasks) {
        final taskId = taskUpdate.taskId?.toInt();
        if (taskId == null) continue;

        final existingTask =
            realm.query<TaskDetailModel>('taskId == \$0', [taskId]).firstOrNull;
        if (existingTask != null) {
          // Update general task fields
          existingTask.projectId = taskUpdate.projectId?.toInt();
          existingTask.scheduleId = taskUpdate.scheduleId?.toInt();
          existingTask.sentToPayroll = taskUpdate.sentToPayroll;
          existingTask.showKm = taskUpdate.showKm;
          existingTask.storeId = taskUpdate.storeId?.toInt();
          existingTask.client = taskUpdate.client;
          existingTask.clientId = taskUpdate.clientId?.toInt();
          existingTask.clientLogoUrl = taskUpdate.clientLogoUrl;
          existingTask.storeGroup = taskUpdate.storeGroup;
          existingTask.storeGroupId = taskUpdate.storeGroupId?.toInt();
          existingTask.storeName = taskUpdate.storeName;
          existingTask.storeEmail = taskUpdate.storeEmail;
          existingTask.minutes = taskUpdate.minutes?.toInt();
          // Track budget changes for recalculation (following Java implementation)
          final oldBudget = existingTask.budget;
          existingTask.budget = taskUpdate.budget?.toInt();
          existingTask.originalbudget = taskUpdate.originalbudget?.toInt();

          // Add to budget calculation list if budget changed
          if (oldBudget != existingTask.budget) {
            taskIDsToCalculateBudget.add(taskId.toString());
          }
          existingTask.comment = taskUpdate.comment;
          existingTask.claimableKms = taskUpdate.claimableKms?.toInt();
          existingTask.flightDuration = taskUpdate.flightDuration?.toInt();
          existingTask.pages = taskUpdate.pages?.toInt();
          existingTask.location = taskUpdate.location;
          existingTask.suburb = taskUpdate.suburb;
          existingTask.latitude = taskUpdate.latitude?.toDouble();
          existingTask.longitude = taskUpdate.longitude?.toDouble();
          existingTask.taskLatitude = taskUpdate.taskLatitude?.toDouble();
          existingTask.taskLongitude = taskUpdate.taskLongitude?.toDouble();
          existingTask.cycle = taskUpdate.cycle;
          existingTask.cycleId = taskUpdate.cycleId?.toInt();
          existingTask.canDelete = taskUpdate.canDelete;
          existingTask.scheduledTimeStamp = taskUpdate.scheduledTimeStamp;
          existingTask.submissionTimeStamp = taskUpdate.submissionTimeStamp;
          existingTask.expires = taskUpdate.expires;
          existingTask.onTask = taskUpdate.onTask;
          existingTask.phone = taskUpdate.phone;
          existingTask.rangeStart = taskUpdate.rangeStart;
          existingTask.rangeEnd = taskUpdate.rangeEnd;
          existingTask.reOpened = taskUpdate.reOpened;
          existingTask.reOpenedReason = taskUpdate.reOpenedReason;
          existingTask.taskStatus = taskUpdate.taskStatus;
          existingTask.warehousejobId = taskUpdate.warehousejobId?.toInt();
          existingTask.connoteUrl = taskUpdate.connoteUrl;
          existingTask.posRequired = taskUpdate.posRequired;
          existingTask.isPosMandatory = taskUpdate.isPosMandatory;
          existingTask.posReceived = taskUpdate.posReceived;
          existingTask.posSentTo = taskUpdate.posSentTo;
          existingTask.posSentToEmail = taskUpdate.posSentToEmail;
          existingTask.modifiedTimeStampTask = taskUpdate.modifiedTimeStampTask;
          existingTask.modifiedTimeStampSignaturetypes =
              taskUpdate.modifiedTimeStampSignaturetypes;
          existingTask.modifiedTimeStampPhototypes =
              taskUpdate.modifiedTimeStampPhototypes;
          existingTask.taskCommencementTimeStamp =
              taskUpdate.taskCommencementTimeStamp;
          existingTask.taskStoppedTimeStamp = taskUpdate.taskStoppedTimeStamp;
          existingTask.teamlead = taskUpdate.teamlead?.toInt();
          existingTask.taskNote = taskUpdate.taskNote;
          existingTask.disallowReschedule = taskUpdate.disallowReschedule;
          existingTask.photoResPerc = taskUpdate.photoResPerc?.toInt();
          existingTask.liveImagesOnly = taskUpdate.liveImagesOnly;
          existingTask.timeSchedule = taskUpdate.timeSchedule;
          existingTask.scheduleTypeId = taskUpdate.scheduleTypeId?.toInt();
          existingTask.showFollowupIconMulti = taskUpdate.showFollowupIconMulti;
          existingTask.followupSelectedMulti = taskUpdate.followupSelectedMulti;
          existingTask.regionId = taskUpdate.regionId?.toInt();
          existingTask.isOpen = taskUpdate.isOpen;
          existingTask.taskCount = taskUpdate.taskCount?.toInt();
          existingTask.preftime = taskUpdate.preftime;
          existingTask.sendTo = taskUpdate.sendTo;
          existingTask.submissionState = taskUpdate.submissionState;
          existingTask.syncPending = taskUpdate.syncPending ?? false;

          // Update related collections if provided
          if (taskUpdate.taskalerts != null) {
            existingTask.taskalerts.clear();
            final tempTaskModel = TaskDetailMapper.toModel(taskUpdate, 0);
            existingTask.taskalerts.addAll(tempTaskModel.taskalerts);
          }
          if (taskUpdate.followupTasks != null) {
            existingTask.followupTasks.clear();
            final tempTaskModel = TaskDetailMapper.toModel(taskUpdate, 0);
            existingTask.followupTasks.addAll(tempTaskModel.followupTasks);
          }
          if (taskUpdate.stocktake != null) {
            existingTask.stocktake.clear();
            final tempTaskModel = TaskDetailMapper.toModel(taskUpdate, 0);
            existingTask.stocktake.addAll(tempTaskModel.stocktake);
          }
          if (taskUpdate.posItems != null) {
            existingTask.posItems.clear();
            final tempTaskModel = TaskDetailMapper.toModel(taskUpdate, 0);
            existingTask.posItems.addAll(tempTaskModel.posItems);
          }
          if (taskUpdate.resumePauseItems != null) {
            existingTask.resumePauseItems.clear();
            final tempTaskModel = TaskDetailMapper.toModel(taskUpdate, 0);
            existingTask.resumePauseItems
                .addAll(tempTaskModel.resumePauseItems);
          }

          logger('📋 Updated task: $taskId');
        } else {
          logger('⚠️ Task not found for task update: $taskId');
        }
      }
    });
  }

  /// Process update_tasks_photos: Update photo-related data for existing tasks
  /// This method mirrors the Java implementation exactly, including:
  /// - Individual photo comparison and updates
  /// - Handling server-only photos (add to local)
  /// - Handling app-only photos (delete if synced)
  /// - Preserving unsynced local photos
  static Future<void> _processUpdateTasksPhotos(
      Realm realm, List<TaskDetail>? updateTasksPhotos) async {
    if (updateTasksPhotos == null || updateTasksPhotos.isEmpty) return;

    logger('📸 Processing ${updateTasksPhotos.length} photo updates');

    realm.write(() {
      for (final taskUpdate in updateTasksPhotos) {
        try {
          final taskId = taskUpdate.taskId?.toInt();
          if (taskId == null) continue;

          final existingTask = realm
              .query<TaskDetailModel>('taskId == \$0', [taskId]).firstOrNull;
          if (existingTask != null) {
            // Create server task model to get the mapped photos
            final serverTaskModel = TaskDetailMapper.toModel(taskUpdate, 0);

            // Step 1: Process existing photos and compare with server photos
            for (final serverPhotoFolderModel in serverTaskModel.photoFolder) {
              for (final serverPhotoModel in serverPhotoFolderModel.photos) {
                // Find corresponding local photo folder
                final localPhotoFolderModel = existingTask.photoFolder
                    .where((folder) =>
                        folder.folderId == serverPhotoFolderModel.folderId)
                    .firstOrNull;

                if (localPhotoFolderModel != null) {
                  // Find existing local photo by photo ID
                  final localPhotoModel = localPhotoFolderModel.photos
                      .where(
                          (photo) => photo.photoId == serverPhotoModel.photoId)
                      .firstOrNull;

                  if (localPhotoModel != null) {
                    // (a) Common photos - check if server has newer version
                    try {
                      if (serverPhotoModel.photoId == localPhotoModel.photoId) {
                        // Compare modification dates - update if server is newer
                        if (localPhotoModel.modifiedTimeStampPhoto == null ||
                            (serverPhotoModel.modifiedTimeStampPhoto != null &&
                                serverPhotoModel.modifiedTimeStampPhoto!
                                    .isAfter(localPhotoModel
                                        .modifiedTimeStampPhoto!))) {
                          // Server has latest information - update local photo
                          _updateLocalPhotoModelByServerModel(
                              localPhotoModel, serverPhotoModel);
                        }
                      }
                    } catch (e) {
                      logger('❌ Error comparing photo dates: $e');
                    }
                  } else {
                    // (c) Server-only photos - add to local
                    try {
                      final newLocalPhoto = PhotoModel();
                      _updateLocalPhotoModelByServerModel(
                          newLocalPhoto, serverPhotoModel);

                      // Add to photo folder
                      localPhotoFolderModel.photos.add(newLocalPhoto);

                      logger(
                          '📸 Added server-only photo: photoId=${serverPhotoModel.photoId}');
                    } catch (e) {
                      logger('❌ Error adding server-only photo: $e');
                    }
                  }
                }
              }
            }

            // Step 2: Handle app-only photos (delete if already synced)
            // Following Java logic: "Never wipe out un-synced photo on app"
            for (final localPhotoFolderModel in existingTask.photoFolder) {
              for (final localPhotoModel in localPhotoFolderModel.photos) {
                // Check if this local photo is synced and exists on server
                if (localPhotoModel.isEdited == false) {
                  // Already synced - check if it exists on server
                  final serverPhotoModel =
                      _findServerPhotoModel(localPhotoModel, serverTaskModel);
                  if (serverPhotoModel == null) {
                    // Photo was deleted on server - mark for deletion
                    localPhotoModel.userDeletedPhoto = true;
                    logger(
                        '📸 Marking app-only synced photo for deletion: photoId=${localPhotoModel.photoId}');
                  }
                }
                // If photo is not synced (isEdited == true), leave it alone to preserve un-synced work
              }
            }

            // Step 3: Update photo-related metadata
            existingTask.modifiedTimeStampPhotos =
                taskUpdate.modifiedTimeStampPhotos;
            existingTask.modifiedTimeStampPhototypes =
                taskUpdate.modifiedTimeStampPhototypes;
            existingTask.photoResPerc = taskUpdate.photoResPerc?.toInt();
            existingTask.liveImagesOnly = taskUpdate.liveImagesOnly;

            logger('📸 Updated photos for task: $taskId');
          } else {
            logger('⚠️ Task not found for photo update: $taskId');
          }
        } catch (e) {
          logger('❌ Error processing photo update: $e');
          // Continue with next task even if this one fails
        }
      }

      // Step 4: Clean up deleted photos from Realm
      _removeDeletedPhotosFromRealm(realm, deleteLocalFiles: true);
    });
  }

  /// Process add_tasks: Add new tasks to local database
  static Future<void> _processAddTasks(Realm realm, List<TaskDetail>? addTasks,
      Set<String> taskIDsToCalculateBudget) async {
    if (addTasks == null || addTasks.isEmpty) return;

    logger('➕ Processing ${addTasks.length} new tasks');

    realm.write(() {
      int nextId = _getNextTaskModelId(realm);

      for (final newTask in addTasks) {
        final taskId = newTask.taskId?.toInt();
        if (taskId == null) continue;

        // Check if task already exists to determine ID
        final existingTask =
            realm.query<TaskDetailModel>('taskId == \$0', [taskId]).firstOrNull;

        final taskModel =
            TaskDetailMapper.toModel(newTask, existingTask?.id ?? nextId++);

        // Use copyToRealmOrUpdate behavior - add or update as needed
        realm.add(taskModel, update: true);

        // Add to budget calculation list (following Java implementation)
        taskIDsToCalculateBudget.add(taskId.toString());

        logger('➕ Added/Updated task: $taskId');
      }
    });
  }

  /// Helper method to log question answer state for submission tracking
  static void _logQuestionAnswerStateForSubmission(
      TaskDetailModel task, int taskId, String stage) {
    logger(
        'SUBMISSION_QA_001: [$stage] Question answers state for task $taskId:');

    int totalQuestionAnswers = 0;
    for (int formIndex = 0; formIndex < task.forms.length; formIndex++) {
      final form = task.forms[formIndex];
      final qaCount = form.questionAnswers.length;
      totalQuestionAnswers += qaCount;

      logger(
          'SUBMISSION_QA_002: [$stage] Form ${form.formId} has $qaCount question answers');

      if (qaCount > 0) {
        for (int qaIndex = 0;
            qaIndex < form.questionAnswers.length;
            qaIndex++) {
          final qa = form.questionAnswers[qaIndex];
          logger(
              'SUBMISSION_QA_003: [$stage] Form ${form.formId} QA[$qaIndex] - questionId: ${qa.questionId}, questionpartId: ${qa.questionpartId}, measurementId: ${qa.measurementId}, measurementTextResult: "${qa.measurementTextResult}", measurementOptionId: ${qa.measurementOptionId}, measurementOptionIds: "${qa.measurementOptionIds}", questionPartMultiId: "${qa.questionPartMultiId}"');
        }
      }
    }

    logger(
        'SUBMISSION_QA_004: [$stage] Task $taskId has TOTAL $totalQuestionAnswers question answers across ${task.forms.length} forms');
  }

  /// Process update_tasks_submission: Update submission-related data for existing tasks
  /// This method mirrors the Java implementation exactly, including form answer processing
  /// and multi-question logic (commented out as requested)
  static Future<void> _processUpdateTasksSubmission(
      Realm realm,
      List<TaskDetail>? updateTasksSubmission,
      Set<String> taskIDsToCalculateBudget) async {
    if (updateTasksSubmission == null || updateTasksSubmission.isEmpty) return;

    logger(
        'SUBMISSION_001: Starting _processUpdateTasksSubmission with ${updateTasksSubmission.length} submission updates');

    realm.write(() {
      for (int updateIndex = 0;
          updateIndex < updateTasksSubmission.length;
          updateIndex++) {
        final taskUpdate = updateTasksSubmission[updateIndex];

        try {
          final taskId = taskUpdate.taskId?.toInt();
          logger(
              'SUBMISSION_002: Processing submission update [$updateIndex] for taskId: $taskId');

          if (taskId == null) {
            logger(
                'SUBMISSION_003: Skipping submission update [$updateIndex] - taskId is null');
            continue;
          }

          final existingTask = realm
              .query<TaskDetailModel>('taskId == \$0', [taskId]).firstOrNull;

          if (existingTask != null) {
            logger(
                'SUBMISSION_004: Found existing task $taskId for submission update');

            // Log question answer state BEFORE processing
            _logQuestionAnswerStateForSubmission(
                existingTask, taskId, 'BEFORE_UPDATE');
            // Update form counter fields (following Java implementation)
            existingTask.ctFormsTotalCnt = taskUpdate.ctFormsTotalCnt?.toInt();
            existingTask.ctFormsCompletedCnt =
                taskUpdate.ctFormsCompletedCnt?.toInt();

            // Add to budget calculation list when answers change (following Java implementation)
            taskIDsToCalculateBudget.add(taskId.toString());

            // Update task detail fields
            existingTask.comment = taskUpdate.comment;
            existingTask.minutes = taskUpdate.minutes?.toInt();
            existingTask.claimableKms = taskUpdate.claimableKms?.toInt();
            existingTask.pages = taskUpdate.pages?.toInt();
            existingTask.taskStatus = taskUpdate.taskStatus;
            existingTask.submissionState = 0; // TASK_SUBMISSION_STATE_DEFAULT

            existingTask.scheduledTimeStamp = taskUpdate.scheduledTimeStamp;
            existingTask.submissionTimeStamp = taskUpdate.submissionTimeStamp;

            // Update coordinates
            existingTask.taskLatitude = 0.0;
            existingTask.taskLongitude = 0.0;
            existingTask.latitude = taskUpdate.latitude?.toDouble();
            existingTask.longitude = taskUpdate.longitude?.toDouble();

            // Process form answers (following Java implementation)
            if (taskUpdate.forms != null) {
              logger(
                  'SUBMISSION_FORM_001: Processing ${taskUpdate.forms!.length} server forms for task $taskId');
              final serverTaskModel = TaskDetailMapper.toModel(taskUpdate, 0);

              for (int serverFormIndex = 0;
                  serverFormIndex < serverTaskModel.forms.length;
                  serverFormIndex++) {
                final serverForm = serverTaskModel.forms[serverFormIndex];
                logger(
                    'SUBMISSION_FORM_002: Processing server form [$serverFormIndex] with formId: ${serverForm.formId}');
                logger(
                    'SUBMISSION_FORM_003: Server form ${serverForm.formId} has ${serverForm.questionAnswers.length} question answers');

                // Log server form question answers
                for (int serverQaIndex = 0;
                    serverQaIndex < serverForm.questionAnswers.length;
                    serverQaIndex++) {
                  final serverQa = serverForm.questionAnswers[serverQaIndex];
                  logger(
                      'SUBMISSION_FORM_004: Server form ${serverForm.formId} QA[$serverQaIndex] - questionId: ${serverQa.questionId}, questionpartId: ${serverQa.questionpartId}, measurementId: ${serverQa.measurementId}, measurementTextResult: "${serverQa.measurementTextResult}"');
                }

                // Find matching local form
                final localForm = existingTask.forms
                    .where((f) => f.formId == serverForm.formId)
                    .firstOrNull;

                if (localForm != null) {
                  final localQuestionAnswersCountBefore =
                      localForm.questionAnswers.length;
                  logger(
                      'SUBMISSION_OP_001: Found matching local form ${localForm.formId} with $localQuestionAnswersCountBefore question answers BEFORE clear operation');

                  // Log local question answers BEFORE clearing
                  if (localQuestionAnswersCountBefore > 0) {
                    logger(
                        'SUBMISSION_OP_002: Local form ${localForm.formId} question answers BEFORE clear:');
                    for (int localQaIndex = 0;
                        localQaIndex < localForm.questionAnswers.length;
                        localQaIndex++) {
                      final localQa = localForm.questionAnswers[localQaIndex];
                      logger(
                          'SUBMISSION_OP_003: Local QA[$localQaIndex] - questionId: ${localQa.questionId}, questionpartId: ${localQa.questionpartId}, measurementId: ${localQa.measurementId}, measurementTextResult: "${localQa.measurementTextResult}"');
                    }
                  }

                  // CRITICAL DESTRUCTIVE OPERATION: Clear and replace question answers
                  localForm.questionAnswers.clear();
                  logger(
                      'SUBMISSION_OP_004: CRITICAL - Cleared all question answers from local form ${localForm.formId} (was $localQuestionAnswersCountBefore answers)');

                  localForm.questionAnswers.addAll(serverForm.questionAnswers);
                  final localQuestionAnswersCountAfter =
                      localForm.questionAnswers.length;
                  logger(
                      'SUBMISSION_FORM_005: Added ${serverForm.questionAnswers.length} server question answers to local form ${localForm.formId}');
                  logger(
                      'SUBMISSION_FORM_006: Local form ${localForm.formId} now has $localQuestionAnswersCountAfter question answers AFTER operation');

                  // Data loss detection
                  if (localQuestionAnswersCountBefore > 0 &&
                      localQuestionAnswersCountAfter == 0) {
                    logger(
                        'SUBMISSION_FORM_007: ⚠️ CRITICAL DATA LOSS WARNING - Form ${localForm.formId} had $localQuestionAnswersCountBefore question answers but now has 0! Server provided no question answers.');
                  } else if (localQuestionAnswersCountBefore >
                      localQuestionAnswersCountAfter) {
                    logger(
                        'SUBMISSION_FORM_008: ⚠️ POTENTIAL DATA LOSS WARNING - Form ${localForm.formId} had $localQuestionAnswersCountBefore question answers but now has $localQuestionAnswersCountAfter');
                  }

                  // Update form status (note: formEdited field doesn't exist in current Dart model)
                  // localForm.isEdited = serverForm.isEdited;
                  localForm.formCompleted = serverForm.formCompleted;
                } else {
                  logger(
                      'SUBMISSION_FORM_009: No matching local form found for server form ${serverForm.formId}');
                }
              }
            } else {
              logger(
                  'SUBMISSION_FORM_010: No server forms to process for task $taskId');
            }

            // multi question qp update begin -------------
            // mo, 20/2/19, answers changed >> multi question's QPs needs to be built again by this answer.
            // Note: This section is commented out as the required functions are not implemented

            if (taskUpdate.forms != null) {
              final serverTaskModel = TaskDetailMapper.toModel(taskUpdate, 0);

              for (final serverForm in serverTaskModel.forms) {
                for (final localForm in existingTask.forms) {
                  if (localForm.formId == serverForm.formId) {
                    for (final questionModel in localForm.questions) {
                      if (questionModel.isMulti == true) {
                        // (a) build new qp from answer
                        // TODO
                        List<QuestionPartModel> newQuestionPartsBuilt = [];
                        for (var qam in localForm.questionAnswers) {
                          if (qam.questionId == questionModel.questionId) {
                            var qmpid = qam.questionPartMultiId;
                            var qpid = qmpid?.split('-')[0];
                            QuestionPartModel? qpm;
                            QuestionModel? qm;
                            qm = localForm.questions
                                .where((q) => q.questionId.toString() == qpid)
                                .firstOrNull;
                            qpm = qm?.questionParts
                                .where((qp) =>
                                    qp.questionpartId.toString() == qpid)
                                .firstOrNull;
                            if (qpm != null) {
                              if (qam.questionId == questionModel.questionId) {
                                QuestionPartModel qpmNew = QuestionPartModel(
                                  questionpartId: qam.questionpartId,
                                  questionpartDescription:
                                      qpm.questionpartDescription,
                                );
                                newQuestionPartsBuilt.add(qpmNew);
                              }
                            }
                          }
                        }

                        // (b) write the new qp into realm
                        // TODO
                        // updateQuestionPartsMultiToDb(questionModel, newQuestionPartsBuilt);
                      }
                    }
                  }
                }
              }
            }

            // multi question qp update end -------------

            // Note: isTicked field doesn't exist in current Dart model
            // existingTask.isTicked = false;

            // Check whether task is completed or not (following Java implementation)
            // Note: isCompleted field doesn't exist in current Dart model
            // final isSuccessful = existingTask.taskStatus == 'Successful';
            // final isUnsuccessful = existingTask.taskStatus == 'Unsuccessful';
            // existingTask.isCompleted = isSuccessful || isUnsuccessful;

            // Reset sync status
            existingTask.syncPending = false;
            // Note: isFailedToSync and syncFailureReason fields don't exist in current Dart model
            // existingTask.isFailedToSync = false;
            // existingTask.syncFailureReason = '';

            // Log question answer state AFTER processing
            _logQuestionAnswerStateForSubmission(
                existingTask, taskId, 'AFTER_UPDATE');

            logger(
                'SUBMISSION_005: Successfully completed submission update for task: $taskId');
          } else {
            logger(
                'SUBMISSION_006: Task not found for submission update: $taskId');
          }
        } catch (e) {
          logger('❌ Error processing submission update: $e');
          // Continue with next task even if this one fails
        }
      }
    });
  }

  /// Process update_tasks_signatures: Update signature-related data for existing tasks
  /// This method mirrors the Java implementation exactly, including:
  /// - Individual signature comparison and updates
  /// - Handling server-only signatures (add to local)
  /// - Handling app-only signatures (delete if synced)
  /// - Preserving unsynced local signatures
  static Future<void> _processUpdateTasksSignatures(
      Realm realm, List<TaskDetail>? updateTasksSignatures) async {
    if (updateTasksSignatures == null || updateTasksSignatures.isEmpty) return;

    logger('✍️ Processing ${updateTasksSignatures.length} signature updates');

    realm.write(() {
      for (final taskUpdate in updateTasksSignatures) {
        try {
          final taskId = taskUpdate.taskId?.toInt();
          if (taskId == null) continue;

          final existingTask = realm
              .query<TaskDetailModel>('taskId == \$0', [taskId]).firstOrNull;
          if (existingTask != null) {
            // Create server task model to get the mapped signatures
            final serverTaskModel = TaskDetailMapper.toModel(taskUpdate, 0);

            // Step 1: Process existing signatures and compare with server signatures
            for (final serverSignatureFolderModel
                in serverTaskModel.signatureFolder) {
              for (final serverSignatureModel
                  in serverSignatureFolderModel.signatures) {
                // Find corresponding local signature folder
                final localSignatureFolderModel = existingTask.signatureFolder
                    .where((folder) =>
                        folder.folderId == serverSignatureFolderModel.folderId)
                    .firstOrNull;

                if (localSignatureFolderModel != null) {
                  // Find existing local signature by signature ID
                  final localSignatureModel = localSignatureFolderModel
                      .signatures
                      .where((signature) =>
                          signature.signatureId ==
                          serverSignatureModel.signatureId)
                      .firstOrNull;

                  if (localSignatureModel != null) {
                    // (a) Common signatures - check if server has newer version
                    try {
                      if (serverSignatureModel.signatureId ==
                          localSignatureModel.signatureId) {
                        // Compare modification dates - update if server is newer
                        if (localSignatureModel.modifiedTimeStampSignature ==
                                null ||
                            (serverSignatureModel.modifiedTimeStampSignature !=
                                    null &&
                                serverSignatureModel.modifiedTimeStampSignature!
                                    .isAfter(localSignatureModel
                                        .modifiedTimeStampSignature!))) {
                          // Server has latest information - update local signature
                          _updateLocalSignatureModelByServerModel(
                              localSignatureModel, serverSignatureModel);
                        }
                      }
                    } catch (e) {
                      logger('❌ Error comparing signature dates: $e');
                    }
                  } else {
                    // (c) Server-only signatures - add to local
                    try {
                      final newLocalSignature = SignatureModel();
                      _updateLocalSignatureModelByServerModel(
                          newLocalSignature, serverSignatureModel);

                      // Add to signature folder
                      localSignatureFolderModel.signatures
                          .add(newLocalSignature);

                      logger(
                          '✍️ Added server-only signature: signatureId=${serverSignatureModel.signatureId}');
                    } catch (e) {
                      logger('❌ Error adding server-only signature: $e');
                    }
                  }
                }
              }
            }

            // Step 2: Handle app-only signatures (delete if already synced)
            // Following Java logic: "Never wipe out un-synced signature on app"
            for (final localSignatureFolderModel
                in existingTask.signatureFolder) {
              for (final localSignatureModel
                  in localSignatureFolderModel.signatures) {
                // Check if this local signature is synced and exists on server
                if (localSignatureModel.isEdited == false) {
                  // Already synced - check if it exists on server
                  final serverSignatureModel = _findServerSignatureModel(
                      localSignatureModel, serverTaskModel);
                  if (serverSignatureModel == null) {
                    // Signature was deleted on server - mark for deletion
                    localSignatureModel.userDeletedSignature = true;
                    logger(
                        '✍️ Marking app-only synced signature for deletion: signatureId=${localSignatureModel.signatureId}');
                  }
                }
                // If signature is not synced (isEdited == true), leave it alone to preserve un-synced work
              }
            }

            // Step 3: Update signature-related metadata
            existingTask.modifiedTimeStampSignatures =
                taskUpdate.modifiedTimeStampSignatures;
            existingTask.modifiedTimeStampSignaturetypes =
                taskUpdate.modifiedTimeStampSignaturetypes;

            logger('✍️ Updated signatures for task: $taskId');
          } else {
            logger('⚠️ Task not found for signature update: $taskId');
          }
        } catch (e) {
          logger('❌ Error processing signature update: $e');
          // Continue with next task even if this one fails
        }
      }

      // Step 4: Clean up deleted signatures from Realm
      _removeDeletedSignatureFromRealm(realm, deleteLocalFiles: true);
    });
  }

  /// Process upload_task_ids: Update sync status for uploaded tasks
  static Future<void> _processUploadTaskIds(
      Realm realm, List<String>? uploadTaskIds) async {
    if (uploadTaskIds == null || uploadTaskIds.isEmpty) return;

    logger('📤 Processing ${uploadTaskIds.length} uploaded task IDs');

    realm.write(() {
      for (final taskIdStr in uploadTaskIds) {
        final taskId = int.tryParse(taskIdStr);
        if (taskId == null) {
          logger('⚠️ Invalid task ID format: $taskIdStr');
          continue;
        }

        final task =
            realm.query<TaskDetailModel>('taskId == \$0', [taskId]).firstOrNull;
        if (task != null) {
          // Mark task as successfully uploaded/synced
          task.syncPending = false;
          task.isSynced = true;
          logger('📤 Marked task as uploaded: $taskId');
        } else {
          logger('⚠️ Task not found for upload status update: $taskId');
        }
      }
    });
  }

  /// Helper method to get the next available ID for TaskDetailModel
  static int _getNextTaskModelId(Realm realm) {
    final allTasks = realm.all<TaskDetailModel>();
    if (allTasks.isEmpty) return 1;

    final maxId =
        allTasks.map((task) => task.id).reduce((a, b) => a > b ? a : b);
    return maxId + 1;
  }

  /// Remove task documents from realm with optional local file deletion
  ///
  /// This method mirrors the Java implementation of removeTaskDocumentsFromRealm()
  /// and handles cleanup of document-related data with optional file deletion.
  ///
  /// Parameters:
  /// - [taskModel]: The TaskDetailModel to clean documents from
  /// - [deleteLocalFiles]: Whether to delete local files (currently commented out)
  ///
  /// Note: File deletion logic is commented out as files are not being saved currently
  static void _removeTaskDocumentsFromRealm(
    TaskDetailModel taskModel,
    bool deleteLocalFiles,
  ) {
    try {
      // Must be called within a realm.write() transaction
      for (var documentModel in taskModel.documents) {
        for (var _ in documentModel.files) {
          if (deleteLocalFiles) {
            // TODO: Uncomment when file saving is implemented
            // Deleting local file
            // try {
            //   if (fileModel.fileLocalPath != null && fileModel.fileLocalPath!.isNotEmpty) {
            //     final localFile = File(fileModel.fileLocalPath!);
            //     if (await localFile.exists()) {
            //       await localFile.delete();
            //     }
            //   }
            // } catch (e) {
            //   logger('Error deleting local file: $e');
            // }
          }
        }
        // Clear document files
        documentModel.files.clear();
      }
      // Clear all documents
      taskModel.documents.clear();
    } catch (e) {
      logger('Error removing task documents from realm: $e');
    }
  }

  /// Create getTasks request entity from database tasks
  ///
  /// This method prepares the request body for the getTasks API,
  /// following the exact structure and parameters from downloadTaskDataSimplified()
  /// method in lib/syncing.dart. It creates a TasksRequestEntity with all necessary
  /// task data including timestamps and metadata.
  ///
  /// Returns a properly structured TasksRequestEntity for the /tasks_optimize endpoint
  static Future<TasksRequestEntity> createGetTasksRequest(
      {String? emulatedUserId}) async {
    final realm = sl<RealmDatabase>().realm;
    final tasks = realm.all<TaskDetailModel>();
    final tasksPost = tasks.map((e) => TaskDetailMapper.toEntity(e)).toList();
    final dataManager = sl<DataManager>();
    final userId = emulatedUserId ?? await dataManager.getUserId() ?? "";
    final token = await dataManager.getAuthToken() ?? "";

    // Build task list with all required fields
    final List<Task> taskList = [];
    for (var task in tasksPost) {
      var taskItem = Task();
      taskItem.taskId = task.taskId?.toString();
      taskItem.scheduledTimeStamp = task.scheduledTimeStamp;
      taskItem.submissionTimeStamp = task.submissionTimeStamp;
      taskItem.modifiedTimeStampTask = task.modifiedTimeStampTask;
      taskItem.modifiedTimeStampPhotos = task.modifiedTimeStampPhotos;
      taskItem.modifiedTimeStampSignatures = task.modifiedTimeStampSignatures;
      taskItem.modifiedTimeStampSignaturetypes =
          task.modifiedTimeStampSignaturetypes ?? AppConstants.minDateTime;
      taskItem.modifiedTimeStampPhototypes =
          task.modifiedTimeStampPhototypes ?? AppConstants.minDateTime;
      taskItem.modifiedTimeStampForms = task.modifiedTimeStampForms;
      taskItem.modifiedTimeStampDocuments = task.modifiedTimeStampDocuments;
      taskItem.phototypeIds = createPhotoTypeIDs(task.photoFolder);
      taskItem.forceImportFollowupTask = false;
      taskList.add(taskItem);
    }

    // Create a new TasksRequestEntity with the populated task list
    return TasksRequestEntity(
      deviceUid: await dataManager.getOrCreateDeviceId(),
      userId: userId,
      appversion: AppConstants.appVersion,
      tasks: taskList,
      token: token,
    );
  }

  /// Process update_phototypes: Update photo type information for existing tasks
  static Future<void> _processUpdatePhototypes(
      Realm realm, List<PhotoTypeListModel>? updatePhototypes) async {
    if (updatePhototypes == null || updatePhototypes.isEmpty) return;

    logger('📷 Processing ${updatePhototypes.length} photo type updates');

    realm.write(() {
      for (final photoTypeUpdate in updatePhototypes) {
        final taskId = int.tryParse(photoTypeUpdate.taskId ?? "");
        if (taskId == null) continue;

        final existingTask =
            realm.query<TaskDetailModel>('taskId == \$0', [taskId]).firstOrNull;
        if (existingTask != null) {
          // Update photo type information for each photo type
          for (final serverPhotoType in photoTypeUpdate.photoTypes ?? []) {
            final photoTypeId = serverPhotoType.phototypeId;

            // Find the corresponding photo folder
            final localPhotoFolder = existingTask.photoFolder
                .where((folder) => folder.folderId.toString() == photoTypeId)
                .firstOrNull;

            if (localPhotoFolder != null) {
              try {
                // Update photo folder properties based on photo type
                localPhotoFolder.attribute = serverPhotoType.mandatory;
                localPhotoFolder.folderName = serverPhotoType.phototypeName;
                localPhotoFolder.folderPictureAmount =
                    serverPhotoType.phototypePictureAmount;
                localPhotoFolder.modifiedTimeStampPhototype =
                    serverPhotoType.modifiedTimeStampPhototype;
              } catch (e) {
                logger('Error updating photo folder properties: $e');
              }
            }
          }

          // Update task's photo types modified timestamp
          existingTask.modifiedTimeStampPhototypes =
              photoTypeUpdate.modifiedTimeStampPhototypes;

          logger('📷 Updated photo types for task: $taskId');
        } else {
          logger('⚠️ Task not found for photo type update: $taskId');
        }
      }
    });
  }

  /// Process add_phototypes: Add new photo types to existing tasks
  static Future<void> _processAddPhototypes(
      Realm realm, List<PhotoTypeListModel>? addPhototypes) async {
    if (addPhototypes == null || addPhototypes.isEmpty) return;

    logger('📷➕ Processing ${addPhototypes.length} photo type additions');

    realm.write(() {
      for (final photoTypeAdd in addPhototypes) {
        final taskId = int.tryParse(photoTypeAdd.taskId ?? "");
        if (taskId == null) continue;

        final existingTask =
            realm.query<TaskDetailModel>('taskId == \$0', [taskId]).firstOrNull;
        if (existingTask != null) {
          // Get existing photo folder list (following Java implementation)
          final existingList = existingTask.photoFolder;

          // Add new photo types as photo folders
          for (final serverPhotoType in photoTypeAdd.photoTypes ?? []) {
            final photoTypeId = serverPhotoType.phototypeId;

            // Check if photo folder already exists (following Java's existingListHasPTAlready logic)
            final existingFolder = existingList
                .where((folder) => folder.folderId.toString() == photoTypeId)
                .firstOrNull;

            if (existingFolder == null) {
              // Create new photo folder for this photo type (following Java implementation)
              final newPhotoFolder = PhotoFolderModel();
              newPhotoFolder.folderId = int.tryParse(photoTypeId ?? "0");
              newPhotoFolder.attribute = serverPhotoType.mandatory;
              newPhotoFolder.folderName = serverPhotoType.phototypeName;
              newPhotoFolder.folderPictureAmount =
                  serverPhotoType.phototypePictureAmount;
              newPhotoFolder.modifiedTimeStampPhototype =
                  serverPhotoType.modifiedTimeStampPhototype;

              // Initialize empty photo folder contents (following Java: setPhotoFolderContents(new RealmList<>()))
              // Note: photos collection is automatically initialized by Realm

              // Create managed PhotoFolderModel and add to existing list (following Java: bgRealm.copyToRealm)
              final managedPhotoFolder = realm.add(newPhotoFolder);
              existingList.add(managedPhotoFolder);

              logger('📷➕ Added new photo type $photoTypeId for task: $taskId');
            } else {
              logger(
                  '⚠️ Photo type $photoTypeId already exists for task: $taskId');
            }
          }

          // Update task's photo types modified timestamp
          existingTask.modifiedTimeStampPhototypes =
              photoTypeAdd.modifiedTimeStampPhototypes;
        } else {
          logger('⚠️ Task not found for photo type addition: $taskId');
        }
      }
    });
  }

  /// Process delete_phototypes: Delete photo types from existing tasks
  static Future<void> _processDeletePhototypes(
      Realm realm, List<PhotoTypeDeleteListModel>? deletePhototypes) async {
    if (deletePhototypes == null || deletePhototypes.isEmpty) return;

    logger('📷🗑️ Processing ${deletePhototypes.length} photo type deletions');

    realm.write(() {
      for (final photoTypeDelete in deletePhototypes) {
        final taskId = int.tryParse(photoTypeDelete.taskId ?? "");
        if (taskId == null) continue;

        final existingTask =
            realm.query<TaskDetailModel>('taskId == \$0', [taskId]).firstOrNull;
        if (existingTask != null) {
          // Delete photo types by removing corresponding photo folders
          for (final photoTypeId in photoTypeDelete.phototypeIDsToBeDeleted) {
            final folderToDelete = existingTask.photoFolder
                .where((folder) => folder.folderId.toString() == photoTypeId)
                .firstOrNull;

            if (folderToDelete != null) {
              // Delete photo folder using Realm's cascading deletion (matches Java implementation)
              realm.delete(folderToDelete);

              logger('📷🗑️ Deleted photo type $photoTypeId for task: $taskId');
            } else {
              logger(
                  '⚠️ Photo type $photoTypeId not found for deletion in task: $taskId');
            }
          }
        } else {
          logger('⚠️ Task not found for photo type deletion: $taskId');
        }
      }
    });
  }

  /// Process update_signaturetypes: Update signature type information for existing tasks
  static Future<void> _processUpdateSignatureTypes(
      Realm realm, List<SignatureTypeListModel>? updateSignatureTypes) async {
    if (updateSignatureTypes == null || updateSignatureTypes.isEmpty) return;

    logger(
        '✍️ Processing ${updateSignatureTypes.length} signature type updates');

    realm.write(() {
      for (final signatureTypeUpdate in updateSignatureTypes) {
        final taskId = int.tryParse(signatureTypeUpdate.taskId ?? "");
        if (taskId == null) continue;

        final existingTask =
            realm.query<TaskDetailModel>('taskId == \$0', [taskId]).firstOrNull;
        if (existingTask != null) {
          // Update signature type information for each signature type
          for (final serverSignatureType
              in signatureTypeUpdate.signatureTypes ?? []) {
            final signatureTypeId = serverSignatureType.signaturetypeId;

            // Find the corresponding signature folder
            final localSignatureFolder = existingTask.signatureFolder
                .where(
                    (folder) => folder.folderId.toString() == signatureTypeId)
                .firstOrNull;

            if (localSignatureFolder != null) {
              // Update signature folder properties based on signature type
              localSignatureFolder.attribute = serverSignatureType.mandatory;
              localSignatureFolder.folderName =
                  serverSignatureType.signaturetypeName;
              localSignatureFolder.modifiedTimeStampSignaturetype =
                  serverSignatureType.modifiedTimeStampSignaturetype;
            }
          }

          // Update task's signature types modified timestamp
          existingTask.modifiedTimeStampSignaturetypes =
              signatureTypeUpdate.modifiedTimeStampSignaturetypes;

          logger('✍️ Updated signature types for task: $taskId');
        } else {
          logger('⚠️ Task not found for signature type update: $taskId');
        }
      }
    });
  }

  /// Process add_signaturetypes: Add new signature types to existing tasks
  static Future<void> _processAddSignatureTypes(
      Realm realm, List<SignatureTypeListModel>? addSignatureTypes) async {
    if (addSignatureTypes == null || addSignatureTypes.isEmpty) return;

    logger(
        '✍️➕ Processing ${addSignatureTypes.length} signature type additions');

    realm.write(() {
      for (final signatureTypeAdd in addSignatureTypes) {
        final taskId = int.tryParse(signatureTypeAdd.taskId ?? "");
        if (taskId == null) continue;

        final existingTask =
            realm.query<TaskDetailModel>('taskId == \$0', [taskId]).firstOrNull;
        if (existingTask != null) {
          // Add new signature types as signature folders
          for (final serverSignatureType
              in signatureTypeAdd.signatureTypes ?? []) {
            final signatureTypeId = serverSignatureType.signaturetypeId;

            // Check if signature folder already exists (following Java existingListHasSIGAlready logic)
            if (!_signatureFolderExists(
                existingTask.signatureFolder, serverSignatureType)) {
              // Create new signature folder for this signature type
              final newSignatureFolder = SignatureFolderModel();
              newSignatureFolder.folderId =
                  int.tryParse(signatureTypeId ?? "0");
              newSignatureFolder.attribute = serverSignatureType.mandatory;
              newSignatureFolder.folderName =
                  serverSignatureType.signaturetypeName;
              newSignatureFolder.modifiedTimeStampSignaturetype =
                  serverSignatureType.modifiedTimeStampSignaturetype;

              // Add to task's signature folders
              existingTask.signatureFolder.add(newSignatureFolder);

              logger(
                  '✍️➕ Added new signature type $signatureTypeId for task: $taskId');
            } else {
              logger(
                  '⚠️ Signature type $signatureTypeId already exists for task: $taskId');
            }
          }

          // Update task's signature types modified timestamp
          existingTask.modifiedTimeStampSignaturetypes =
              signatureTypeAdd.modifiedTimeStampSignaturetypes;
        } else {
          logger('⚠️ Task not found for signature type addition: $taskId');
        }
      }
    });
  }

  /// Process delete_signaturetypes: Delete signature types from existing tasks
  static Future<void> _processDeleteSignatureTypes(Realm realm,
      List<SignatureTypeDeleteListModel>? deleteSignatureTypes) async {
    if (deleteSignatureTypes == null || deleteSignatureTypes.isEmpty) return;

    logger(
        '✍️🗑️ Processing ${deleteSignatureTypes.length} signature type deletions');

    realm.write(() {
      for (final signatureTypeDelete in deleteSignatureTypes) {
        final taskId = int.tryParse(signatureTypeDelete.taskId ?? "");
        if (taskId == null) continue;

        final existingTask =
            realm.query<TaskDetailModel>('taskId == \$0', [taskId]).firstOrNull;
        if (existingTask != null) {
          // Delete signature types by removing corresponding signature folders
          for (final signatureTypeId
              in signatureTypeDelete.signaturetypeIDsToBeDeleted) {
            final folderToDelete = existingTask.signatureFolder
                .where(
                    (folder) => folder.folderId.toString() == signatureTypeId)
                .firstOrNull;

            if (folderToDelete != null) {
              // Delete signature folder using Realm's cascading deletion (matches Java implementation)
              realm.delete(folderToDelete);

              logger(
                  '✍️🗑️ Deleted signature type $signatureTypeId for task: $taskId');
            } else {
              logger(
                  '⚠️ Signature type $signatureTypeId not found for deletion in task: $taskId');
            }
          }
        } else {
          logger('⚠️ Task not found for signature type deletion: $taskId');
        }
      }
    });
  }

  /// Helper method to find matching server photo model for a local photo
  static PhotoModel? _findServerPhotoModel(
      PhotoModel localPhoto, TaskDetailModel serverTask) {
    for (final photoFolder in serverTask.photoFolder) {
      for (final serverPhoto in photoFolder.photos) {
        // Match by photoId if both exist
        if (localPhoto.photoId != null && serverPhoto.photoId != null) {
          if (localPhoto.photoId == serverPhoto.photoId) {
            return serverPhoto;
          }
        }
        // Match by form, question, and measurement IDs if photoId is null
        if (localPhoto.formId == serverPhoto.formId &&
            localPhoto.questionId == serverPhoto.questionId &&
            localPhoto.measurementId == serverPhoto.measurementId &&
            localPhoto.measurementPhototypeId ==
                serverPhoto.measurementPhototypeId) {
          return serverPhoto;
        }
      }
    }
    return null;
  }

  /// Helper method to update local photo model with server photo model data
  static void _updateLocalPhotoModelByServerModel(
      PhotoModel localPhoto, PhotoModel serverPhoto) {
    try {
      // Delete local photo file if it exists (following Java implementation)
      if (localPhoto.localPath != null && localPhoto.localPath!.isNotEmpty) {
        _deleteLocalPhotoFile(localPhoto.localPath!);
      }

      // Update photo model with server data
      localPhoto.photoId = serverPhoto.photoId;
      localPhoto.formId = serverPhoto.formId;
      localPhoto.questionId = serverPhoto.questionId;
      localPhoto.measurementId = serverPhoto.measurementId;
      localPhoto.measurementPhototypeId = serverPhoto.measurementPhototypeId;
      localPhoto.questionpartId = serverPhoto.questionpartId;
      localPhoto.questionPartMultiId = serverPhoto.questionPartMultiId;
      localPhoto.combineTypeId = serverPhoto.combineTypeId;
      localPhoto.caption = serverPhoto.caption;
      localPhoto.photoUrl = serverPhoto.photoUrl;
      localPhoto.localPath = null; // Will be downloaded later if needed
      localPhoto.cannotUploadMandatory = serverPhoto.cannotUploadMandatory;
      localPhoto.modifiedTimeStampPhoto = serverPhoto.modifiedTimeStampPhoto;
      localPhoto.isEdited = false;
      localPhoto.userDeletedPhoto = false;

      logger(
          '📸 Updated local photo model with server data: photoId=${serverPhoto.photoId}');
    } catch (e) {
      logger('❌ Error updating local photo model: $e');
    }
  }

  /// Helper method to delete local photo file
  static void _deleteLocalPhotoFile(String localPath) {
    try {
      final file = File(localPath);
      if (file.existsSync()) {
        file.deleteSync();
        logger('📸🗑️ Deleted local photo file: $localPath');
      }
    } catch (e) {
      logger('❌ Error deleting local photo file $localPath: $e');
    }
  }

  /// Helper method to remove deleted photos from Realm
  static void _removeDeletedPhotosFromRealm(Realm realm,
      {bool deleteLocalFiles = true}) {
    try {
      final deletedPhotos =
          realm.query<PhotoModel>('userDeletedPhoto == true').toList();

      for (final photo in deletedPhotos) {
        if (deleteLocalFiles &&
            photo.localPath != null &&
            photo.localPath!.isNotEmpty) {
          _deleteLocalPhotoFile(photo.localPath!);
        }

        // Remove photo from its parent folder
        final photoFolders = realm.query<PhotoFolderModel>('photos.@count > 0');
        for (final folder in photoFolders) {
          folder.photos.removeWhere((p) => p.photoId == photo.photoId);
        }

        realm.delete(photo);
        logger(
            '📸🗑️ Removed deleted photo from Realm: photoId=${photo.photoId}');
      }
    } catch (e) {
      logger('❌ Error removing deleted photos from Realm: $e');
    }
  }

  /// Helper method to find matching server signature model for a local signature
  static SignatureModel? _findServerSignatureModel(
      SignatureModel localSignature, TaskDetailModel serverTask) {
    for (final signatureFolder in serverTask.signatureFolder) {
      for (final serverSignature in signatureFolder.signatures) {
        // Match by signatureId if both exist
        if (localSignature.signatureId != null &&
            serverSignature.signatureId != null) {
          if (localSignature.signatureId == serverSignature.signatureId) {
            return serverSignature;
          }
        }
        // Match by form and question IDs if signatureId is null
        if (localSignature.formId == serverSignature.formId &&
            localSignature.questionId == serverSignature.questionId) {
          return serverSignature;
        }
      }
    }
    return null;
  }

  /// Helper method to update local signature model with server signature model data
  static void _updateLocalSignatureModelByServerModel(
      SignatureModel localSignature, SignatureModel serverSignature) {
    try {
      // Delete local signature file if it exists (following Java implementation)
      if (localSignature.localPath != null &&
          localSignature.localPath!.isNotEmpty) {
        _deleteLocalSignatureFile(localSignature.localPath!);
      }

      // Update signature model with server data
      localSignature.signatureId = serverSignature.signatureId;
      localSignature.cannotUploadMandatory =
          serverSignature.cannotUploadMandatory;
      localSignature.signedBy = serverSignature.signedBy;
      localSignature.questionId = serverSignature.questionId;
      localSignature.signatureUrl = serverSignature.signatureUrl;
      localSignature.localPath = null; // Will be downloaded later if needed
      localSignature.modifiedTimeStampSignature =
          serverSignature.modifiedTimeStampSignature;
      localSignature.isEdited = false;
      localSignature.userDeletedSignature = false;

      logger(
          '✍️ Updated local signature model with server data: signatureId=${serverSignature.signatureId}');
    } catch (e) {
      logger('❌ Error updating local signature model: $e');
    }
  }

  /// Helper method to delete local signature file
  static void _deleteLocalSignatureFile(String localPath) {
    try {
      final file = File(localPath);
      if (file.existsSync()) {
        file.deleteSync();
        logger('✍️🗑️ Deleted local signature file: $localPath');
      }
    } catch (e) {
      logger('❌ Error deleting local signature file $localPath: $e');
    }
  }

  /// Helper method to remove deleted signatures from Realm
  static void _removeDeletedSignatureFromRealm(Realm realm,
      {bool deleteLocalFiles = true}) {
    try {
      final deletedSignatures =
          realm.query<SignatureModel>('userDeletedSignature == true').toList();

      for (final signature in deletedSignatures) {
        if (deleteLocalFiles &&
            signature.localPath != null &&
            signature.localPath!.isNotEmpty) {
          _deleteLocalSignatureFile(signature.localPath!);
        }

        // Remove signature from its parent folder
        final signatureFolders =
            realm.query<SignatureFolderModel>('signatures.@count > 0');
        for (final folder in signatureFolders) {
          folder.signatures
              .removeWhere((s) => s.signatureId == signature.signatureId);
        }

        realm.delete(signature);
        logger(
            '✍️🗑️ Removed deleted signature from Realm: signatureId=${signature.signatureId}');
      }
    } catch (e) {
      logger('❌ Error removing deleted signatures from Realm: $e');
    }
  }

  /// Helper method to check if a signature folder already exists in the existing list
  /// Replicates the Java existingListHasSIGAlready() logic
  static bool _signatureFolderExists(List<SignatureFolderModel> existingFolders,
      SignatureTypeModel serverSignatureType) {
    final signatureTypeId = serverSignatureType.signaturetypeId;

    for (final folder in existingFolders) {
      if (folder.folderId.toString() == signatureTypeId) {
        return true;
      }
    }
    return false;
  }

  /// Create photo type IDs list from photo folders
  ///
  /// This method replicates the Java createPhotoTypeIDs() method that extracts
  /// photo folder IDs from a list of PhotoFolder entities.
  ///
  /// Parameters:
  /// - [photoFolders]: List of PhotoFolder entities containing folder IDs
  ///
  /// Returns a list of photo type IDs as strings, or empty list if input is null/empty
  static List<String> createPhotoTypeIDs(List<PhotoFolder>? photoFolders) {
    final List<String> result = [];

    if (photoFolders == null || photoFolders.isEmpty) {
      return result;
    }

    for (final photoFolder in photoFolders) {
      final folderId = photoFolder.folderId;
      if (folderId != null) {
        result.add(folderId.toString());
      }
    }

    return result;
  }

  /// Downloads files for offline use after sync completion
  ///
  /// This method orchestrates the download of all file types needed for offline functionality:
  /// - Client logos
  /// - Photo URLs
  /// - Signature URLs
  /// - Question photos
  /// - Document files
  /// - User photos
  ///
  /// Files are downloaded asynchronously and failures don't block sync completion.
  static Future<void> _downloadFilesForOfflineUse() async {
    try {
      logger('📁 Starting file downloads for offline use');

      // Execute all download methods concurrently for better performance
      await Future.wait([
        _downloadClientLogos(),
        _downloadPhotoUrls(),
        _downloadSignatureUrls(),
        _downloadQuestionPhotos(),
        _downloadDocumentFiles(),
        _downloadUserPhotos(),
      ], eagerError: false); // Continue even if some downloads fail

      logger('✅ File downloads for offline use completed');
    } catch (e) {
      logger('❌ Error during file downloads for offline use: $e');
      // Don't throw - file downloads are not critical for sync completion
    }
  }

  /// Download client logo URLs and store them locally
  static Future<void> _downloadClientLogos() async {
    try {
      logger('🏢 Starting client logo downloads');

      final realm = sl<RealmDatabase>().realm;
      final tasks = realm.query<TaskDetailModel>('clientLogoUrl != null');

      int downloadCount = 0;
      for (final task in tasks) {
        if (task.clientLogoUrl?.isNotEmpty == true) {
          try {
            // Download and save client logo
            final localPath = await _downloadAndSaveFile(
              task.clientLogoUrl!,
              'logos',
              'client_logo_${task.taskId}',
            );

            if (localPath != null) {
              downloadCount++;
              logger(
                  '🏢 Downloaded client logo for task ${task.taskId}: $localPath');
            }
          } catch (e) {
            logger(
                '❌ Failed to download client logo for task ${task.taskId}: $e');
          }
        }
      }

      logger('🏢 Client logo downloads completed: $downloadCount files');
    } catch (e) {
      logger('❌ Error downloading client logos: $e');
    }
  }

  /// Download photo URLs and store them locally
  static Future<void> _downloadPhotoUrls() async {
    try {
      logger('📸 Starting photo URL downloads');

      final realm = sl<RealmDatabase>().realm;
      final photoFolders = realm.query<PhotoFolderModel>('photos.@count > 0');

      int downloadCount = 0;
      for (final folder in photoFolders) {
        for (final photo in folder.photos) {
          if (photo.photoUrl?.isNotEmpty == true) {
            try {
              // Check if already downloaded locally
              if (photo.localPath?.isNotEmpty == true) {
                final localFile = File(photo.localPath!);
                if (await localFile.exists()) {
                  continue; // Skip if already exists
                }
              }

              // Download and save photo
              final localPath = await _downloadAndSaveFile(
                photo.photoUrl!,
                'photos',
                'photo_${photo.photoId}',
              );

              if (localPath != null) {
                // Update photo with local path
                // realm.write(() {
                //   photo.localPath = localPath;
                // });
                downloadCount++;
                logger('📸 Downloaded photo ${photo.photoId}');
              }
            } catch (e) {
              logger('❌ Failed to download photo ${photo.photoId}: $e');
            }
          }
        }
      }

      logger('📸 Photo URL downloads completed: $downloadCount files');
    } catch (e) {
      logger('❌ Error downloading photo URLs: $e');
    }
  }

  /// Download signature URLs and store them locally
  static Future<void> _downloadSignatureUrls() async {
    try {
      logger('✍️ Starting signature URL downloads');

      final realm = sl<RealmDatabase>().realm;
      final signatureFolders =
          realm.query<SignatureFolderModel>('signatures.@count > 0');

      int downloadCount = 0;
      for (final folder in signatureFolders) {
        for (final signature in folder.signatures) {
          if (signature.signatureUrl?.isNotEmpty == true) {
            try {
              // Check if already downloaded locally
              if (signature.localPath?.isNotEmpty == true) {
                final localFile = File(signature.localPath!);
                if (await localFile.exists()) {
                  continue; // Skip if already exists
                }
              }

              // Download and save signature
              final localPath = await _downloadAndSaveFile(
                signature.signatureUrl!,
                'signatures',
                'signature_${signature.signatureId}',
              );

              if (localPath != null) {
                // Update signature with local path
                // realm.write(() {
                //   signature.localPath = localPath;
                // });
                downloadCount++;
                logger('✍️ Downloaded signature ${signature.signatureId}');
              }
            } catch (e) {
              logger(
                  '❌ Failed to download signature ${signature.signatureId}: $e');
            }
          }
        }
      }

      logger('✍️ Signature URL downloads completed: $downloadCount files');
    } catch (e) {
      logger('❌ Error downloading signature URLs: $e');
    }
  }

  /// Download question photo URLs and store them locally
  static Future<void> _downloadQuestionPhotos() async {
    try {
      final realm = sl<RealmDatabase>().realm;
      final questions = realm.query<QuestionModel>('photoUrl != null');

      final Set<String> urls = {};
      for (final q in questions) {
        if (q.photoUrl?.isNotEmpty == true) urls.add(q.photoUrl!);
      }

      int count = 0;
      for (final url in urls) {
        final path = await _downloadAndSaveFile(
          url,
          'question_photos',
          'photo_${url.hashCode}',
        );
        if (path != null) count++;
      }

      logger('❓📸 Downloaded $count question photos');
    } catch (e) {
      logger('❌ Error downloading question photos: $e');
    }
  }

  /// Check if a URL points to a video file based on its extension
  static bool _isVideoFile(String url) {
    final extension = path.extension(url).toLowerCase();
    const videoExtensions = {
      '.mp4',
      '.mov',
      '.avi',
      '.mkv',
      '.webm',
      '.m4v',
      '.3gp',
      '.flv',
      '.wmv'
    };
    return videoExtensions.contains(extension) || url.contains('youtube');
  }

  /// Download document files and store them locally
  static Future<void> _downloadDocumentFiles() async {
    try {
      final realm = sl<RealmDatabase>().realm;
      final files = realm.query<FileElementModel>('documentFileLink != null');

      final Set<String> urls = {};
      for (final f in files) {
        if (f.documentFileLink?.isNotEmpty == true) {
          urls.add(f.documentFileLink!);
        }
      }

      int count = 0;
      int skippedVideos = 0;
      for (final url in urls) {
        if (_isVideoFile(url)) {
          skippedVideos++;
          continue;
        }

        final path = await _downloadAndSaveFile(
          url,
          'documents',
          'file_${url.hashCode}',
        );
        if (path != null) count++;
      }

      logger(
          '📄 Downloaded $count document files (skipped $skippedVideos videos)');
    } catch (e) {
      logger('❌ Error downloading document files: $e');
    }
  }

  /// Download user photo URLs and store them locally
  static Future<void> _downloadUserPhotos() async {
    try {
      final realm = sl<RealmDatabase>().realm;
      final profiles = realm.query<ProfileModel>('profileImageUrl != null');

      final Set<String> urls = {};
      for (final p in profiles) {
        if (p.profileImageUrl?.isNotEmpty == true) urls.add(p.profileImageUrl!);
      }

      int count = 0;
      for (final url in urls) {
        final path = await _downloadAndSaveFile(
          url,
          'user_photos',
          'user_${url.hashCode}',
        );
        if (path != null) count++;
      }

      logger('👤 Downloaded $count user photos');
    } catch (e) {
      logger('❌ Error downloading user photos: $e');
    }
  }

  /// Helper method to download a file from URL and save it locally
  ///
  /// Parameters:
  /// - [url]: The URL to download from
  /// - [subfolder]: Subfolder to organize files (logos, photos, etc.)
  /// - [baseFilename]: Base filename without extension
  ///
  /// Returns the local file path if successful, null if failed
  static Future<String?> _downloadAndSaveFile(
    String url,
    String subfolder,
    String baseFilename,
  ) async {
    try {
      // Skip video files (including YouTube links) entirely
      if (_isVideoFile(url)) {
        return null;
      }

      final realm = sl<RealmDatabase>().realm;

      // Check if file already downloaded
      final existingFile = realm.find<DownloadedFileModel>(url);
      if (existingFile != null && existingFile.isDownloaded) {
        final file = File(existingFile.localPath);
        if (await file.exists()) {
          // Update last access date
          realm.write(() {
            existingFile.lastAccessDate = DateTime.now();
          });
          return existingFile.localPath;
        } else {
          // File doesn't exist, mark as not downloaded
          realm.write(() {
            existingFile.isDownloaded = false;
          });
        }
      }

      // Download file bytes directly using ApiClient's Dio instance
      // The ApiClient already has built-in retry logic via RetryInterceptor
      final apiClient = sl<ApiClient>();
      final dio = apiClient.instance;

      final response = await dio.get(
        url,
        options: Options(
          responseType: ResponseType.bytes,
          followRedirects: true,
          validateStatus: (status) => status != null && status < 400,
        ),
      );

      if (response.statusCode != 200) {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          message: 'Failed to download file: HTTP ${response.statusCode}',
        );
      }

      final bytes = response.data as List<int>;

      // Determine file extension from URL or default to jpg
      final uri = Uri.parse(url);
      String extension = path.extension(uri.path).toLowerCase();
      if (extension.isEmpty) {
        extension = '.jpg'; // Default for images
      }

      // Create directory structure
      final appDocDir = await getApplicationDocumentsDirectory();
      final filesDir = Directory(path.join(appDocDir.path, 'files', subfolder));

      if (!await filesDir.exists()) {
        await filesDir.create(recursive: true);
      }

      // Create temporary file path for atomic operation
      final fileName = '$baseFilename$extension';
      final filePath = path.join(filesDir.path, fileName);
      final tempFilePath = '$filePath.tmp';

      // Write to temporary file first (atomic operation)
      final tempFile = File(tempFilePath);
      await tempFile.writeAsBytes(bytes);

      // Validate file size
      final downloadedSize = await tempFile.length();
      if (downloadedSize != bytes.length) {
        await tempFile.delete();
        throw Exception(
            'File size mismatch: expected ${bytes.length}, got $downloadedSize');
      }

      // Atomically rename temp file to final location
      final finalFile = await tempFile.rename(filePath);

      // Save metadata to database
      final downloadedFile = DownloadedFileModel(
        url, // id
        subfolder, // fileType
        url, // originalUrl
        finalFile.path, // localPath
        DateTime.now(), // downloadDate
        true, // isDownloaded
        fileSize: bytes.length,
        lastAccessDate: DateTime.now(),
      );

      realm.write(() {
        realm.add(downloadedFile, update: true);
      });

      return finalFile.path;
    } catch (e) {
      logger('❌ Error downloading and saving file from $url: $e');

      // Cleanup any temporary files
      try {
        final uri = Uri.parse(url);
        String extension = path.extension(uri.path).toLowerCase();
        if (extension.isEmpty) {
          extension = '.jpg';
        }
        final appDocDir = await getApplicationDocumentsDirectory();
        final tempFilePath = path.join(
            appDocDir.path, 'files', subfolder, '$baseFilename$extension.tmp');
        final tempFile = File(tempFilePath);
        if (await tempFile.exists()) {
          await tempFile.delete();
        }
      } catch (_) {
        // Ignore cleanup errors
      }

      return null;
    }
  }
}
