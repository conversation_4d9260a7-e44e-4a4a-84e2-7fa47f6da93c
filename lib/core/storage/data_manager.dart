import 'package:storetrack_app/features/auth/data/models/login_response.dart';
import 'package:storetrack_app/core/services/device_info_service.dart';

import 'storage_service.dart';

/// Interface for token management
abstract class DataManager {
  // Save login response
  Future<void> saveLoginResponse(LoginResponse loginResponse);

  // Get login response
  Future<LoginResponse?> getLoginResponse();

  /// Save auth token
  Future<void> saveAuthToken(String token);

  /// Get auth token
  Future<String?> getAuthToken();

  /// Save FCM token
  Future<void> saveFcmToken(String token);

  /// Get FCM token
  Future<String?> getFcmToken();

  /// Clear all data
  Future<void> clearAll();

  // Save email
  Future<void> saveEmail(String email);

  // Get email
  Future<String?> getEmail();

  // Get user id
  Future<String?> getUserId();

  // Save task order
  Future<void> saveTaskOrder(List<String> taskIds);

  // Get task order
  Future<List<String>?> getTaskOrder();
  Future<void> saveNotificationEnabled(bool enabled);

  // Get notification setting
  Future<bool> getNotificationEnabled();

  // Save tutorial setting
  Future<void> saveTutorialEnabled(bool enabled);

  // Get tutorial setting
  Future<bool> getTutorialEnabled();

  // Save tutorial 1 setting
  Future<void> saveTutorial1Enabled(bool enabled);

  // Get tutorial 1 setting
  Future<bool> getTutorial1Enabled();

  // Day tracking methods
  Future<void> saveDayStarted(bool started);
  Future<bool> getDayStarted();
  Future<void> saveStartTime(int timestamp);
  Future<int?> getStartTime();

  // Device ID methods
  Future<void> saveDeviceId(String deviceId);
  Future<String?> getDeviceId();
  Future<String> getOrCreateDeviceId();

  // Last sync time methods
  Future<void> saveLastSyncTime(DateTime syncTime);
  Future<DateTime?> getLastSyncTime();

  // Original user info methods (for emulation)
  Future<void> saveOriginalUserName(String firstName, String lastName);
  Future<Map<String, String>?> getOriginalUserName();
  Future<void> setEmulating(bool isEmulating);
  Future<bool> isEmulating();
  Future<void> clearOriginalUserData();

  // Alert count methods
  Future<void> saveAlertCount(int count);
  Future<int> getAlertCount();

  // User state preference methods
  Future<void> saveAdminAccess(bool adminAccess);
  Future<bool> getAdminAccess();
  Future<void> saveCreateTask(bool createTask);
  Future<bool> getCreateTask();
  Future<void> saveSupportNumber(String supportNumber);
  Future<String?> getSupportNumber();
}

/// A simple implementation of [DataManager]
class DataManagerImpl implements DataManager {
  /// Constructor
  DataManagerImpl(this._storage, this._deviceInfoService);

  final StorageService _storage;
  final DeviceInfoService _deviceInfoService;

  static const _loginResponseKey = "Constants.loginResponse";
  static const _authTokenKey = "Constants.authToken";
  static const _fcmTokenKey = "Constants.fcmToken";
  static const _taskOrderKey = "Constants.taskOrder";
  static const _notificationEnabledKey = "Constants.notificationEnabled";
  static const _tutorialEnabledKey = "Constants.tutorialEnabled";
  static const _dayStartedKey = "Constants.dayStarted";
  static const _startTimeKey = "Constants.startTime";
  static const _deviceIdKey = "Constants.deviceId";
  static const _lastSyncTimeKey = "Constants.lastSyncTime";
  static const _originalFirstNameKey = "Constants.originalFirstName";
  static const _originalLastNameKey = "Constants.originalLastName";
  static const _isEmulatingKey = "Constants.isEmulating";
  static const _alertCountKey = "Constants.alertCount";
  static const _adminAccessKey = "Constants.adminAccess";
  static const _createTaskKey = "Constants.createTask";
  static const _supportNumberKey = "Constants.supportNumber";

  @override
  Future<void> clearAll() async {
    await _storage.clearAll();
  }

  @override
  Future<LoginResponse?> getLoginResponse() async {
    final value =
        await _storage.readData<Map<String, dynamic>>(_loginResponseKey);
    if (value != null) {
      return LoginResponse.fromJson(value);
    }
    return null;
  }

  @override
  Future<void> saveLoginResponse(LoginResponse loginResponse) {
    var loginResponseJson = loginResponse.toJson();
    return _storage.writeData(_loginResponseKey, loginResponseJson);
  }

  @override
  Future<void> saveAuthToken(String token) {
    return _storage.writeData(_authTokenKey, token);
  }

  @override
  Future<String?> getAuthToken() async {
    return await _storage.readData(_authTokenKey) as String?;
  }

  @override
  Future<void> saveFcmToken(String token) {
    return _storage.writeData(_fcmTokenKey, token);
  }

  @override
  Future<String?> getFcmToken() async {
    return await _storage.readData(_fcmTokenKey) as String?;
  }

  @override
  Future<void> saveEmail(String email) {
    return _storage.writeData("email", email);
  }

  @override
  Future<String?> getEmail() async {
    return await _storage.readData("email") as String?;
  }

  @override
  Future<String?> getUserId() async {
    return await getLoginResponse()
        .then((value) => value?.data?.userId.toString());
  }

  @override
  Future<void> saveTaskOrder(List<String> taskIds) {
    return _storage.writeData(_taskOrderKey, taskIds);
  }

  @override
  Future<List<String>?> getTaskOrder() async {
    final value = await _storage.readData<List<dynamic>>(_taskOrderKey);
    if (value != null) {
      return value.cast<String>();
    }
    return null;
  }

  @override
  Future<void> saveNotificationEnabled(bool enabled) {
    return _storage.writeData(_notificationEnabledKey, enabled);
  }

  @override
  Future<bool> getNotificationEnabled() async {
    final value = await _storage.readData<bool>(_notificationEnabledKey);
    return value ?? true; // Default to true if not set
  }

  @override
  Future<void> saveTutorialEnabled(bool enabled) {
    return _storage.writeData(_tutorialEnabledKey, enabled);
  }

  @override
  Future<bool> getTutorialEnabled() async {
    final value = await _storage.readData<bool>(_tutorialEnabledKey);
    return value ?? true; // Default to true if not set
  }

  @override
  Future<bool> getTutorial1Enabled() {
    // TODO: implement getTutorial1Enabled
    throw UnimplementedError();
  }

  @override
  Future<void> saveTutorial1Enabled(bool enabled) {
    // TODO: implement saveTutorial1Enabled
    throw UnimplementedError();
  }

  @override
  Future<void> saveDayStarted(bool started) {
    return _storage.writeData(_dayStartedKey, started);
  }

  @override
  Future<bool> getDayStarted() async {
    final value = await _storage.readData<bool>(_dayStartedKey);
    return value ?? false; // Default to false if not set
  }

  @override
  Future<void> saveStartTime(int timestamp) {
    return _storage.writeData(_startTimeKey, timestamp);
  }

  @override
  Future<int?> getStartTime() async {
    return await _storage.readData<int>(_startTimeKey);
  }

  @override
  Future<void> saveDeviceId(String deviceId) {
    return _storage.writeData(_deviceIdKey, deviceId);
  }

  @override
  Future<String?> getDeviceId() async {
    return await _storage.readData(_deviceIdKey) as String?;
  }

  @override
  Future<String> getOrCreateDeviceId() async {
    // return "8b7a6774c878a206";
    final existingId = await getDeviceId();
    if (existingId != null && existingId.isNotEmpty) {
      return existingId;
    }

    // Generate new device ID and save it
    final deviceId = await _deviceInfoService.getDeviceId();
    await saveDeviceId(deviceId);
    return deviceId;
  }

  @override
  Future<void> saveLastSyncTime(DateTime syncTime) {
    return _storage.writeData(
        _lastSyncTimeKey, syncTime.millisecondsSinceEpoch);
  }

  @override
  Future<DateTime?> getLastSyncTime() async {
    final timestamp = await _storage.readData<int>(_lastSyncTimeKey);
    if (timestamp != null) {
      return DateTime.fromMillisecondsSinceEpoch(timestamp);
    }
    return null;
  }

  @override
  Future<void> saveOriginalUserName(String firstName, String lastName) async {
    await _storage.writeData(_originalFirstNameKey, firstName);
    await _storage.writeData(_originalLastNameKey, lastName);
  }

  @override
  Future<Map<String, String>?> getOriginalUserName() async {
    final firstName = await _storage.readData<String>(_originalFirstNameKey);
    final lastName = await _storage.readData<String>(_originalLastNameKey);

    if (firstName != null && lastName != null) {
      return {
        'firstName': firstName,
        'lastName': lastName,
      };
    }
    return null;
  }

  @override
  Future<void> setEmulating(bool isEmulating) {
    return _storage.writeData(_isEmulatingKey, isEmulating);
  }

  @override
  Future<bool> isEmulating() async {
    final value = await _storage.readData<bool>(_isEmulatingKey);
    return value ?? false;
  }

  @override
  Future<void> clearOriginalUserData() async {
    await _storage.deleteData(_originalFirstNameKey);
    await _storage.deleteData(_originalLastNameKey);
    await _storage.writeData(_isEmulatingKey, false);
  }

  @override
  Future<void> saveAlertCount(int count) {
    return _storage.writeData(_alertCountKey, count);
  }

  @override
  Future<int> getAlertCount() async {
    final value = await _storage.readData<int>(_alertCountKey);
    return value ?? 0; // Default to 0 if not set
  }

  @override
  Future<void> saveAdminAccess(bool adminAccess) {
    return _storage.writeData(_adminAccessKey, adminAccess);
  }

  @override
  Future<bool> getAdminAccess() async {
    final value = await _storage.readData<bool>(_adminAccessKey);
    return value ?? false; // Default to false if not set
  }

  @override
  Future<void> saveCreateTask(bool createTask) {
    return _storage.writeData(_createTaskKey, createTask);
  }

  @override
  Future<bool> getCreateTask() async {
    final value = await _storage.readData<bool>(_createTaskKey);
    return value ?? false; // Default to false if not set
  }

  @override
  Future<void> saveSupportNumber(String supportNumber) {
    return _storage.writeData(_supportNumberKey, supportNumber);
  }

  @override
  Future<String?> getSupportNumber() async {
    return await _storage.readData(_supportNumberKey) as String?;
  }
}
