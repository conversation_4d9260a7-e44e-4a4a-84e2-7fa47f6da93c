import 'dart:math';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:package_info_plus/package_info_plus.dart';
import '../../firebase_options.dart';
import '../utils/logger.dart';
import '../network/api_client.dart';
import '../storage/data_manager.dart';
import 'device_info_service.dart';
import '../../di/service_locator.dart';
import '../../config/routes/app_router.dart';
import '../../config/routes/app_router.gr.dart';
import '../../features/home/<USER>/blocs/alert_count/alert_count_cubit.dart';

// This handler must be a top-level function (not a class method).
@pragma('vm:entry-point')
Future<void> handleBackgroundMessage(RemoteMessage message) async {
  // You MUST initialize Firebase in the background isolate.
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  logger('🔑 [Background] Received message: ${message.messageId}');

  logger(
      '🔄 [Background] Processing message without DataManager due to isolate limitations');
  final data = message.data;
  final notification = message.notification;
  final String title = data['message'] ?? notification?.title ?? "StoreTrack";
  final String body =
      data['context'] ?? notification?.body ?? "You have a new notification!";

  logger('📱 [Background] Notification: $title - $body');
}

class FirebaseService {
  static FirebaseMessaging? _messaging;
  static final FlutterLocalNotificationsPlugin _localNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  static const AndroidNotificationDetails _androidNotificationDetails =
      AndroidNotificationDetails(
    'storetrack',
    'storetrack',
    channelDescription: 'Default channel for notifications',
    importance: Importance.max,
    priority: Priority.high,
    showWhen: true,
  );
  static const DarwinNotificationDetails _iosNotificationDetails =
      DarwinNotificationDetails(
    presentAlert: true,
    presentBadge: true,
    presentSound: true,
  );
  static const NotificationDetails _platformNotificationDetails =
      NotificationDetails(
    android: _androidNotificationDetails,
    iOS: _iosNotificationDetails,
  );

  /// One-time setup for Firebase core services. Call this in main.dart before runApp().
  static Future<void> setupApp() async {
    try {
      await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform,
      );
      logger('✅ Firebase initialized successfully');

      _messaging = _getFirebaseMessaging();
      if (_messaging != null) {
        FirebaseMessaging.onBackgroundMessage(handleBackgroundMessage);
      }
    } catch (e) {
      logger('❌ Firebase initialization failed: $e');
    }
  }

  /// Initializes listeners and permissions. Call this from a widget with a BuildContext.
  static Future<void> initialize(BuildContext context) async {
    if (_messaging == null) {
      logger('Firebase messaging not available, skipping setup');
      return;
    }
    try {
      _printFcmToken();
      await requestPushPermissions();
      await _initLocalNotifications();
      await _initListeners();
      await _setupTokenRefreshListener();
      await _refreshTokenOnStartup();

      // Handle app launch from a terminated state
      final initialMessage = await _messaging!.getInitialMessage();
      if (initialMessage != null) {
        _handleNotificationTap(initialMessage, isTerminated: true);
      }
    } catch (e) {
      logger('Error initializing push notifications: $e');
    }
  }

  static FirebaseMessaging? _getFirebaseMessaging() {
    try {
      return Firebase.apps.isNotEmpty ? FirebaseMessaging.instance : null;
    } catch (e) {
      logger('Firebase not available for messaging: $e');
      return null;
    }
  }

  static Future<bool> requestPushPermissions() async {
    if (_messaging == null) return false;
    try {
      final settings = await _messaging!.requestPermission(
        alert: true,
        badge: true,
        sound: true,
      );
      logger('User granted permission: ${settings.authorizationStatus}');
      return settings.authorizationStatus == AuthorizationStatus.authorized;
    } catch (e) {
      logger('Error requesting push permissions: $e');
      return false;
    }
  }

  static Future<void> _initLocalNotifications() async {
    const androidSettings =
        AndroidInitializationSettings('@mipmap/ic_launcher');
    const iosSettings = DarwinInitializationSettings(
        requestAlertPermission: true,
        requestBadgePermission: true,
        requestSoundPermission: true);
    const initSettings =
        InitializationSettings(android: androidSettings, iOS: iosSettings);

    await _localNotificationsPlugin.initialize(
      initSettings,
      onDidReceiveNotificationResponse: (response) =>
          _handleNotificationTap(null),
    );
  }

  static Future<void> _initListeners() async {
    if (_messaging == null) return;

    await _messaging!.setForegroundNotificationPresentationOptions(
      alert: true,
      badge: true,
      sound: true,
    );

    // App is in the background and user taps the notification.
    FirebaseMessaging.onMessageOpenedApp.listen((message) {
      _handleNotificationTap(message, isBackground: true);
    });

    // App is in the foreground when a notification arrives.
    FirebaseMessaging.onMessage.listen((RemoteMessage message) async {
      logger('📱 [Foreground] Received message: ${message.messageId}');

      final notificationContent = await processRemoteMessage(
        message: message,
        dataManager: sl<DataManager>(),
        isForeground: true, // Notify UI to update
      );

      await createPush(
        title: notificationContent['title']!,
        body: notificationContent['body']!,
      );
    });
  }

  /// Centralized logic to process a message payload.
  /// This can be called from both foreground and background handlers.
  static Future<Map<String, String>> processRemoteMessage({
    required RemoteMessage message,
    required DataManager dataManager,
    bool isForeground = false,
  }) async {
    final data = message.data;
    final notification = message.notification;

    final String title = data['message'] ?? notification?.title ?? "StoreTrack";
    final String body =
        data['context'] ?? notification?.body ?? "You have a new notification!";

    if (data['type']?.toLowerCase() == 'alert') {
      try {
        final currentCount = await dataManager.getAlertCount();
        await dataManager.saveAlertCount(currentCount + 1);
        logger('📊 Alert count incremented to: ${currentCount + 1}');

        if (isForeground) {
          sl<AlertCountCubit>().loadAlertCount();
          logger('🔄 UI notified of alert count change');
        }
      } catch (e) {
        logger('⚠️ Error incrementing alert count: $e');
      }
    }

    return {'title': title, 'body': body};
  }

  /// Centralized handler for any action that should result from tapping a notification.
  static void _handleNotificationTap(RemoteMessage? message,
      {bool isTerminated = false, bool isBackground = false}) {
    String state = isTerminated
        ? "terminated"
        : (isBackground ? "background" : "foreground");
    logger('🔔 Notification tapped while app was in $state.');
    _navigateToNotifications();
  }

  static Future<void> createPush(
      {required String title, required String body}) async {
    try {
      final bool notificationEnabled =
          await sl<DataManager>().getNotificationEnabled();
      if (!notificationEnabled) {
        logger('Notifications disabled by user, skipping.');
        return;
      }
      _localNotificationsPlugin.show(
        Random().nextInt(100000),
        title,
        body,
        _platformNotificationDetails,
      );
    } catch (e) {
      logger('Error showing local notification: $e');
    }
  }

  static void _navigateToNotifications() {
    try {
      sl<AppRouter>().replaceAll(
        [
          HomeRoute(children: [
            DashboardHolderRoute(children: [
              DashboardRoute(skipSync: true),
              const NotificationsRoute(),
            ]),
          ]),
        ],
        updateExistingRoutes: false,
      );
      logger('🔔 Navigated to notifications page.');
    } catch (e) {
      logger('❌ Error navigating to notifications page: $e');
    }
  }

  static Future<String?> getToken() async {
    if (_messaging == null) return null;
    try {
      final token = await _messaging!.getToken();
      if (token != null) {
        await sl<DataManager>().saveFcmToken(token);
      }
      return token;
    } catch (e) {
      logger('Error getting FCM token: $e');
      return null;
    }
  }

  static Future<void> _printFcmToken() async {
    final token = await getToken();
    logger('FCM Token: ${token.toString()}');
  }

  static Future<void> _setupTokenRefreshListener() async {
    if (_messaging == null) return;
    _messaging!.onTokenRefresh.listen((fcmToken) async {
      logger('🔄 FCM Token refreshed: $fcmToken');
      final storedToken = await sl<DataManager>().getFcmToken();
      if (storedToken != fcmToken) {
        await updateDeviceToken(fcmToken);
      }
    }).onError((e) => logger('❌ Error in token refresh listener: $e'));
  }

  static Future<void> _refreshTokenOnStartup() async {
    try {
      final currentToken = await getToken();
      if (currentToken != null && await sl<DataManager>().getUserId() != null) {
        await updateDeviceToken(currentToken);
      }
    } catch (e) {
      logger('⚠️ Error refreshing token on startup: $e');
    }
  }

  static Future<void> updateDeviceToken(String newDeviceToken) async {
    try {
      final dataManager = sl<DataManager>();
      final userId = await dataManager.getUserId();
      final fcmToken = await getToken();

      if (userId == null || fcmToken == null) {
        logger('❌ Cannot update token: missing user ID or FCM token.');
        return;
      }

      final deviceInfo = sl<DeviceInfoService>();
      final packageInfo = await PackageInfo.fromPlatform();

      final requestBody = {
        'user_id': userId,
        'token': fcmToken,
        'device_uid': await dataManager.getOrCreateDeviceId(),
        'device_token': newDeviceToken,
        'device_platform': deviceInfo.getDevicePlatform().toLowerCase() == 'ios'
            ? 'iOS'
            : 'Android',
        'appversion': packageInfo.version,
      };

      logger('🔄 Updating device token on server...');
      final response = await sl<ApiClient>().instance.post(
            '/update_device_token',
            data: requestBody,
          );

      if (response.statusCode == 200) {
        logger('✅ Device token updated successfully');
      } else {
        logger('❌ Device token update failed: ${response.statusCode}');
      }
    } catch (e) {
      logger('❌ Error updating device token: $e');
    }
  }
}
